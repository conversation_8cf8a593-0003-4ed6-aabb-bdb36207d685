import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Base configuration class."""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))  # 16MB
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'temp')
    
    # Model configuration
    MODEL_NAME = os.environ.get('MODEL_NAME', 'cardiffnlp/twitter-roberta-base-sentiment-latest')
    DEVICE = os.environ.get('DEVICE', 'cpu')
    
    # File extensions
    ALLOWED_EXTENSIONS = set(os.environ.get('ALLOWED_EXTENSIONS', 'pdf,doc,docx,txt,png,jpg,jpeg,gif,mp3,wav,m4a').split(','))
    
    # Translation settings
    DEFAULT_TARGET_LANGUAGE = os.environ.get('DEFAULT_TARGET_LANGUAGE', 'en')
    AUTO_TRANSLATE = os.environ.get('AUTO_TRANSLATE', 'true').lower() == 'true'
    
    # API settings
    API_RATE_LIMIT = int(os.environ.get('API_RATE_LIMIT', 100))

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    FLASK_ENV = 'development'

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    FLASK_ENV = 'production'

class TestingConfig(Config):
    """Testing configuration."""
    TESTING = True
    DEBUG = True

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
