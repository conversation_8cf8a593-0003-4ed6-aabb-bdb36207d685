<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multilingual Fake News Detection System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card-hover {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .result-card {
            border-left: 5px solid;
            animation: slideIn 0.5s ease-out;
        }
        .real-news {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .fake-news {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }
        .loading-spinner {
            display: none;
        }
        .confidence-bar {
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
        }
        .stats-card {
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark gradient-bg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-shield-alt me-2"></i>
                Fake News Detector
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#detector">Detector</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#samples">Samples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="gradient-bg py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-robot me-3"></i>
                        AI-Powered Fake News Detection
                    </h1>
                    <p class="lead mb-4">
                        Advanced multilingual system using fine-tuned RoBERTa model to detect fake news from text, images, documents, and audio files.
                    </p>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stats-card p-3 rounded">
                                <h3 class="fw-bold">107</h3>
                                <small>Languages</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stats-card p-3 rounded">
                                <h3 class="fw-bold">4</h3>
                                <small>Input Types</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stats-card p-3 rounded">
                                <h3 class="fw-bold">85%</h3>
                                <small>Accuracy</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 text-center">
                    <i class="fas fa-newspaper feature-icon text-white-50"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Detector Section -->
    <section id="detector" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <div class="card shadow-lg card-hover">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-search me-2"></i>
                                Fake News Detection
                            </h3>
                        </div>
                        <div class="card-body">
                            <form id="detectionForm" enctype="multipart/form-data">
                                <div class="mb-3">
                                    <label for="fileInput" class="form-label">
                                        <i class="fas fa-upload me-2"></i>
                                        Upload File
                                    </label>
                                    <input type="file" class="form-control" id="fileInput" name="file" 
                                           accept=".txt,.pdf,.doc,.docx,.png,.jpg,.jpeg,.gif,.mp3,.wav,.m4a">
                                    <div class="form-text">
                                        Supported: Text files, PDFs, Images (OCR), Audio files
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="typeSelect" class="form-label">
                                        <i class="fas fa-cog me-2"></i>
                                        File Type
                                    </label>
                                    <select class="form-select" id="typeSelect" name="type">
                                        <option value="auto">Auto-detect</option>
                                        <option value="document">Document</option>
                                        <option value="image">Image (OCR)</option>
                                        <option value="audio">Audio</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="autoTranslate" name="auto_translate" checked>
                                        <label class="form-check-label" for="autoTranslate">
                                            <i class="fas fa-language me-2"></i>
                                            Auto-translate to English
                                        </label>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-magic me-2"></i>
                                    Analyze for Fake News
                                </button>
                            </form>

                            <!-- Loading Spinner -->
                            <div class="loading-spinner text-center mt-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Analyzing content...</p>
                            </div>

                            <!-- Results -->
                            <div id="results" class="mt-4" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5 bg-light">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-star me-2"></i>
                Key Features
            </h2>
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="card h-100 text-center card-hover">
                        <div class="card-body">
                            <i class="fas fa-robot feature-icon text-primary"></i>
                            <h5>RoBERTa Model</h5>
                            <p>Fine-tuned multilingual RoBERTa for accurate fake news detection</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card h-100 text-center card-hover">
                        <div class="card-body">
                            <i class="fas fa-file-alt feature-icon text-success"></i>
                            <h5>Multi-Format</h5>
                            <p>Supports text, PDF, images (OCR), and audio files</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card h-100 text-center card-hover">
                        <div class="card-body">
                            <i class="fas fa-globe feature-icon text-info"></i>
                            <h5>Multilingual</h5>
                            <p>Supports 107 languages with automatic translation</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card h-100 text-center card-hover">
                        <div class="card-body">
                            <i class="fas fa-shield-alt feature-icon text-warning"></i>
                            <h5>Secure</h5>
                            <p>File validation, size limits, and malware detection</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Sample Data Section -->
    <section id="samples" class="py-5">
        <div class="container">
            <h2 class="text-center mb-5">
                <i class="fas fa-vials me-2"></i>
                Try Sample Data
            </h2>
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card card-hover">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-check-circle me-2"></i>
                                Real News Sample
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                "TechCorp, a leading technology company, announced today the launch of their new AI initiative..."
                            </p>
                            <button class="btn btn-success" onclick="testSample('real')">
                                <i class="fas fa-play me-2"></i>
                                Test This Sample
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card card-hover">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Fake News Sample
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="card-text">
                                "SHOCKING: Scientists discover that 100% of people who drink water eventually DIE!"
                            </p>
                            <button class="btn btn-danger" onclick="testSample('fake')">
                                <i class="fas fa-play me-2"></i>
                                Test This Sample
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="gradient-bg text-white py-4">
        <div class="container text-center">
            <p class="mb-0">
                <i class="fas fa-code me-2"></i>
                Multilingual Fake News Detection System - Powered by RoBERTa & Flask
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form submission handler
        document.getElementById('detectionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('fileInput');
            const typeSelect = document.getElementById('typeSelect');
            const autoTranslate = document.getElementById('autoTranslate');
            
            if (!fileInput.files[0]) {
                alert('Please select a file to analyze.');
                return;
            }
            
            formData.append('file', fileInput.files[0]);
            formData.append('type', typeSelect.value);
            formData.append('auto_translate', autoTranslate.checked ? 'true' : 'false');
            
            // Show loading spinner
            document.querySelector('.loading-spinner').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            
            try {
                const response = await fetch('/api/detect', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                // Hide loading spinner
                document.querySelector('.loading-spinner').style.display = 'none';
                
                if (response.ok) {
                    displayResults(result);
                } else {
                    displayError(result.error || 'An error occurred');
                }
            } catch (error) {
                document.querySelector('.loading-spinner').style.display = 'none';
                displayError('Network error: ' + error.message);
            }
        });
        
        function displayResults(result) {
            const resultsDiv = document.getElementById('results');
            const isReal = result.prediction === 'Real';
            const confidence = (result.confidence * 100).toFixed(1);
            
            resultsDiv.innerHTML = `
                <div class="result-card card ${isReal ? 'real-news' : 'fake-news'}">
                    <div class="card-body">
                        <h4 class="card-title">
                            <i class="fas ${isReal ? 'fa-check-circle text-success' : 'fa-exclamation-triangle text-danger'} me-2"></i>
                            Prediction: ${result.prediction} News
                        </h4>
                        
                        <div class="mb-3">
                            <label class="form-label">Confidence Level</label>
                            <div class="confidence-bar bg-light">
                                <div class="bg-${isReal ? 'success' : 'danger'} h-100" style="width: ${confidence}%"></div>
                            </div>
                            <small class="text-muted">${confidence}% confident</small>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-language me-2"></i>Language Detected</h6>
                                <p>${result.language_detected || 'Unknown'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-clock me-2"></i>Processing Time</h6>
                                <p>${result.processing_time?.total || 0}s</p>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6><i class="fas fa-file-text me-2"></i>Extracted Text Preview</h6>
                            <p class="text-muted small">${result.extracted_text || 'No text extracted'}</p>
                        </div>
                        
                        ${result.translation_performed ? 
                            '<div class="alert alert-info"><i class="fas fa-language me-2"></i>Text was automatically translated to English for analysis</div>' : 
                            ''
                        }
                    </div>
                </div>
            `;
            
            resultsDiv.style.display = 'block';
        }
        
        function displayError(error) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Error:</strong> ${error}
                </div>
            `;
            resultsDiv.style.display = 'block';
        }
        
        async function testSample(type) {
            try {
                // Show loading
                document.querySelector('.loading-spinner').style.display = 'block';
                document.getElementById('results').style.display = 'none';

                // Fetch the sample file from our API
                const fileResponse = await fetch(`/api/sample/${type}`);
                if (!fileResponse.ok) {
                    throw new Error('Failed to fetch sample file');
                }

                // Get the file as a blob
                const blob = await fileResponse.blob();
                const file = new File([blob], `${type}_news_sample.txt`, { type: 'text/plain' });

                // Create form data
                const formData = new FormData();
                formData.append('file', file);
                formData.append('type', 'document');
                formData.append('auto_translate', 'true');

                // Submit to API
                const apiResponse = await fetch('/api/detect', {
                    method: 'POST',
                    body: formData
                });

                const result = await apiResponse.json();

                document.querySelector('.loading-spinner').style.display = 'none';

                if (apiResponse.ok) {
                    displayResults(result);
                } else {
                    displayError(result.error || 'An error occurred');
                }

                // Scroll to results
                document.getElementById('results').scrollIntoView({ behavior: 'smooth' });

            } catch (error) {
                document.querySelector('.loading-spinner').style.display = 'none';
                displayError('Error testing sample: ' + error.message);
            }
        }
    </script>
</body>
</html>
