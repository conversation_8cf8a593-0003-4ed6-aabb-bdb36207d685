"""
Validation utilities for file uploads and input sanitization
"""

import os
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path

try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileValidator:
    """
    Comprehensive file validation for security and compatibility.
    """
    
    def __init__(self):
        """Initialize the file validator."""
        # File size limits (in bytes)
        self.max_file_sizes = {
            'image': 10 * 1024 * 1024,    # 10MB for images
            'document': 50 * 1024 * 1024,  # 50MB for documents
            'audio': 100 * 1024 * 1024,    # 100MB for audio
            'default': 16 * 1024 * 1024    # 16MB default
        }
        
        # Allowed MIME types
        self.allowed_mime_types = {
            'image': {
                'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 
                'image/bmp', 'image/tiff', 'image/webp'
            },
            'document': {
                'application/pdf', 'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'text/plain', 'application/rtf'
            },
            'audio': {
                'audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/ogg',
                'audio/flac', 'audio/aac', 'audio/x-m4a'
            }
        }
        
        # Dangerous file extensions to block
        self.blocked_extensions = {
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs',
            '.js', '.jar', '.app', '.deb', '.pkg', '.dmg', '.sh',
            '.ps1', '.msi', '.dll', '.so', '.dylib'
        }
        
        # Check if python-magic is available
        self.magic_available = MAGIC_AVAILABLE
        if not self.magic_available:
            logger.warning("python-magic not available, using extension-based validation")
    
    def validate_file(self, file_path: str, expected_type: str = "auto") -> Dict:
        """
        Comprehensive file validation.
        
        Args:
            file_path: Path to the file to validate
            expected_type: Expected file type ('image', 'document', 'audio', 'auto')
            
        Returns:
            Dictionary with validation results
        """
        if not os.path.exists(file_path):
            return {
                "valid": False,
                "error": "File does not exist",
                "file_type": None,
                "size": 0,
                "mime_type": None
            }
        
        try:
            # Get file info
            file_size = os.path.getsize(file_path)
            file_ext = Path(file_path).suffix.lower()
            
            # Check for blocked extensions
            if file_ext in self.blocked_extensions:
                return {
                    "valid": False,
                    "error": f"File extension {file_ext} is not allowed for security reasons",
                    "file_type": None,
                    "size": file_size,
                    "mime_type": None
                }
            
            # Detect MIME type
            mime_type = self._get_mime_type(file_path)
            
            # Determine file type
            detected_type = self._determine_file_type(file_ext, mime_type)
            
            if expected_type != "auto" and detected_type != expected_type:
                return {
                    "valid": False,
                    "error": f"File type mismatch: expected {expected_type}, detected {detected_type}",
                    "file_type": detected_type,
                    "size": file_size,
                    "mime_type": mime_type
                }
            
            # Validate file size
            size_validation = self._validate_file_size(file_size, detected_type)
            if not size_validation["valid"]:
                return {
                    "valid": False,
                    "error": size_validation["error"],
                    "file_type": detected_type,
                    "size": file_size,
                    "mime_type": mime_type
                }
            
            # Validate MIME type
            mime_validation = self._validate_mime_type(mime_type, detected_type)
            if not mime_validation["valid"]:
                return {
                    "valid": False,
                    "error": mime_validation["error"],
                    "file_type": detected_type,
                    "size": file_size,
                    "mime_type": mime_type
                }
            
            # Additional security checks
            security_check = self._security_scan(file_path, detected_type)
            if not security_check["valid"]:
                return {
                    "valid": False,
                    "error": security_check["error"],
                    "file_type": detected_type,
                    "size": file_size,
                    "mime_type": mime_type
                }
            
            return {
                "valid": True,
                "error": None,
                "file_type": detected_type,
                "size": file_size,
                "mime_type": mime_type,
                "extension": file_ext
            }
            
        except Exception as e:
            logger.error(f"Error validating file {file_path}: {str(e)}")
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}",
                "file_type": None,
                "size": 0,
                "mime_type": None
            }
    
    def _get_mime_type(self, file_path: str) -> Optional[str]:
        """Get MIME type of the file."""
        if self.magic_available:
            try:
                return magic.from_file(file_path, mime=True)
            except Exception as e:
                logger.warning(f"Magic MIME detection failed: {str(e)}")
        
        # Fallback to extension-based detection
        ext = Path(file_path).suffix.lower()
        mime_map = {
            '.pdf': 'application/pdf',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.txt': 'text/plain',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.mp3': 'audio/mpeg',
            '.wav': 'audio/wav',
            '.m4a': 'audio/x-m4a'
        }
        return mime_map.get(ext, 'application/octet-stream')
    
    def _determine_file_type(self, extension: str, mime_type: str) -> str:
        """Determine file type based on extension and MIME type."""
        # Check MIME type first
        for file_type, mime_types in self.allowed_mime_types.items():
            if mime_type in mime_types:
                return file_type
        
        # Fallback to extension
        image_exts = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp'}
        document_exts = {'.pdf', '.doc', '.docx', '.txt', '.rtf'}
        audio_exts = {'.mp3', '.wav', '.m4a', '.ogg', '.flac', '.aac'}
        
        if extension in image_exts:
            return 'image'
        elif extension in document_exts:
            return 'document'
        elif extension in audio_exts:
            return 'audio'
        else:
            return 'unknown'
    
    def _validate_file_size(self, file_size: int, file_type: str) -> Dict:
        """Validate file size against limits."""
        max_size = self.max_file_sizes.get(file_type, self.max_file_sizes['default'])
        
        if file_size > max_size:
            return {
                "valid": False,
                "error": f"File size ({file_size} bytes) exceeds maximum allowed size ({max_size} bytes) for {file_type} files"
            }
        
        if file_size == 0:
            return {
                "valid": False,
                "error": "File is empty"
            }
        
        return {"valid": True, "error": None}
    
    def _validate_mime_type(self, mime_type: str, file_type: str) -> Dict:
        """Validate MIME type against allowed types."""
        if file_type == 'unknown':
            return {
                "valid": False,
                "error": f"Unknown or unsupported file type (MIME: {mime_type})"
            }
        
        allowed_types = self.allowed_mime_types.get(file_type, set())
        
        if mime_type not in allowed_types:
            return {
                "valid": False,
                "error": f"MIME type {mime_type} not allowed for {file_type} files"
            }
        
        return {"valid": True, "error": None}
    
    def _security_scan(self, file_path: str, file_type: str) -> Dict:
        """Basic security scanning of the file."""
        try:
            # Check file header for common malicious patterns
            with open(file_path, 'rb') as f:
                header = f.read(1024)  # Read first 1KB
            
            # Check for executable signatures
            executable_signatures = [
                b'MZ',      # Windows PE
                b'\x7fELF', # Linux ELF
                b'\xfe\xed\xfa', # macOS Mach-O
                b'\xcf\xfa\xed\xfe', # macOS Mach-O
            ]
            
            for sig in executable_signatures:
                if header.startswith(sig):
                    return {
                        "valid": False,
                        "error": "File appears to be an executable, which is not allowed"
                    }
            
            # Additional checks for specific file types
            if file_type == 'document':
                # Check for embedded scripts in documents (basic check)
                if b'<script' in header.lower() or b'javascript:' in header.lower():
                    return {
                        "valid": False,
                        "error": "Document contains potentially malicious scripts"
                    }
            
            return {"valid": True, "error": None}
            
        except Exception as e:
            logger.warning(f"Security scan failed for {file_path}: {str(e)}")
            # Don't fail validation if security scan fails
            return {"valid": True, "error": None}
    
    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename to prevent path traversal and other issues.
        
        Args:
            filename: Original filename
            
        Returns:
            Sanitized filename
        """
        if not filename:
            return "unnamed_file"
        
        # Remove path components
        filename = os.path.basename(filename)
        
        # Remove or replace dangerous characters
        dangerous_chars = '<>:"/\\|?*'
        for char in dangerous_chars:
            filename = filename.replace(char, '_')
        
        # Remove leading/trailing dots and spaces
        filename = filename.strip('. ')
        
        # Ensure filename is not empty
        if not filename:
            filename = "unnamed_file"
        
        # Limit length
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename

# Global validator instance
_global_validator = None

def get_validator() -> FileValidator:
    """
    Get a global validator instance (singleton pattern).
    
    Returns:
        FileValidator instance
    """
    global _global_validator
    
    if _global_validator is None:
        _global_validator = FileValidator()
    
    return _global_validator
