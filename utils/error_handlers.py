"""
Error handling utilities and middleware for the Flask application
"""

import logging
import traceback
from flask import jsonify, request
from werkzeug.exceptions import RequestEntityTooLarge, BadRequest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def register_error_handlers(app):
    """
    Register error handlers for the Flask application.
    
    Args:
        app: Flask application instance
    """
    
    @app.errorhandler(400)
    def bad_request(error):
        """Handle 400 Bad Request errors."""
        logger.warning(f"Bad request: {request.url} - {str(error)}")
        return jsonify({
            'error': 'Bad Request',
            'message': 'The request could not be understood by the server',
            'status_code': 400
        }), 400
    
    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 Not Found errors."""
        logger.warning(f"Not found: {request.url}")
        return jsonify({
            'error': 'Not Found',
            'message': 'The requested resource was not found',
            'status_code': 404
        }), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        """Handle 405 Method Not Allowed errors."""
        logger.warning(f"Method not allowed: {request.method} {request.url}")
        return jsonify({
            'error': 'Method Not Allowed',
            'message': f'The {request.method} method is not allowed for this endpoint',
            'status_code': 405
        }), 405
    
    @app.errorhandler(413)
    def request_entity_too_large(error):
        """Handle 413 Request Entity Too Large errors."""
        logger.warning(f"File too large: {request.url}")
        return jsonify({
            'error': 'File Too Large',
            'message': 'The uploaded file exceeds the maximum allowed size',
            'max_size': app.config.get('MAX_CONTENT_LENGTH', 16777216),
            'status_code': 413
        }), 413
    
    @app.errorhandler(415)
    def unsupported_media_type(error):
        """Handle 415 Unsupported Media Type errors."""
        logger.warning(f"Unsupported media type: {request.url}")
        return jsonify({
            'error': 'Unsupported Media Type',
            'message': 'The uploaded file type is not supported',
            'supported_types': list(app.config.get('ALLOWED_EXTENSIONS', [])),
            'status_code': 415
        }), 415
    
    @app.errorhandler(429)
    def rate_limit_exceeded(error):
        """Handle 429 Too Many Requests errors."""
        logger.warning(f"Rate limit exceeded: {request.remote_addr}")
        return jsonify({
            'error': 'Rate Limit Exceeded',
            'message': 'Too many requests. Please try again later.',
            'status_code': 429
        }), 429
    
    @app.errorhandler(500)
    def internal_server_error(error):
        """Handle 500 Internal Server Error."""
        logger.error(f"Internal server error: {request.url} - {str(error)}")
        logger.error(traceback.format_exc())
        
        # Don't expose internal error details in production
        if app.config.get('DEBUG', False):
            return jsonify({
                'error': 'Internal Server Error',
                'message': str(error),
                'traceback': traceback.format_exc(),
                'status_code': 500
            }), 500
        else:
            return jsonify({
                'error': 'Internal Server Error',
                'message': 'An unexpected error occurred. Please try again later.',
                'status_code': 500
            }), 500
    
    @app.errorhandler(RequestEntityTooLarge)
    def handle_file_too_large(error):
        """Handle file upload size limit exceeded."""
        logger.warning(f"File upload too large: {request.url}")
        return jsonify({
            'error': 'File Too Large',
            'message': 'The uploaded file is too large',
            'max_size_bytes': app.config.get('MAX_CONTENT_LENGTH', 16777216),
            'max_size_mb': app.config.get('MAX_CONTENT_LENGTH', 16777216) / (1024 * 1024),
            'status_code': 413
        }), 413
    
    @app.errorhandler(Exception)
    def handle_unexpected_error(error):
        """Handle any unexpected errors."""
        logger.error(f"Unexpected error: {request.url} - {str(error)}")
        logger.error(traceback.format_exc())
        
        # Don't expose internal error details in production
        if app.config.get('DEBUG', False):
            return jsonify({
                'error': 'Unexpected Error',
                'message': str(error),
                'type': type(error).__name__,
                'traceback': traceback.format_exc(),
                'status_code': 500
            }), 500
        else:
            return jsonify({
                'error': 'Unexpected Error',
                'message': 'An unexpected error occurred. Please try again later.',
                'status_code': 500
            }), 500

def log_request_info():
    """Log information about incoming requests."""
    logger.info(f"{request.method} {request.url} - {request.remote_addr}")
    
    # Log file upload info if present
    if request.files:
        for key, file in request.files.items():
            if file.filename:
                logger.info(f"File upload: {key} = {file.filename} ({file.content_type})")

def validate_request_size(app):
    """Validate request size before processing."""
    max_size = app.config.get('MAX_CONTENT_LENGTH', 16777216)
    
    if request.content_length and request.content_length > max_size:
        logger.warning(f"Request too large: {request.content_length} bytes (max: {max_size})")
        return jsonify({
            'error': 'Request Too Large',
            'message': f'Request size ({request.content_length} bytes) exceeds maximum allowed size ({max_size} bytes)',
            'status_code': 413
        }), 413
    
    return None

def setup_request_middleware(app):
    """
    Set up request middleware for logging and validation.
    
    Args:
        app: Flask application instance
    """
    
    @app.before_request
    def before_request():
        """Execute before each request."""
        # Log request info
        log_request_info()
        
        # Validate request size
        size_validation = validate_request_size(app)
        if size_validation:
            return size_validation
    
    @app.after_request
    def after_request(response):
        """Execute after each request."""
        # Log response info
        logger.info(f"Response: {response.status_code} - {request.url}")
        
        # Add security headers
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        return response

class APIException(Exception):
    """Custom exception for API errors."""
    
    def __init__(self, message, status_code=400, payload=None):
        """
        Initialize API exception.
        
        Args:
            message: Error message
            status_code: HTTP status code
            payload: Additional error data
        """
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.payload = payload
    
    def to_dict(self):
        """Convert exception to dictionary."""
        result = {'error': self.message}
        if self.payload:
            result.update(self.payload)
        return result

def register_api_exception_handler(app):
    """Register handler for custom API exceptions."""
    
    @app.errorhandler(APIException)
    def handle_api_exception(error):
        """Handle custom API exceptions."""
        logger.warning(f"API Exception: {error.message} (status: {error.status_code})")
        response = jsonify(error.to_dict())
        response.status_code = error.status_code
        return response
