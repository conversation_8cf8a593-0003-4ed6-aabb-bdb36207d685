"""
Language detection utilities
"""

import re
import logging

logger = logging.getLogger(__name__)

def detect_language(text):
    """
    Simple language detection based on patterns.
    
    Args:
        text (str): Text to analyze
        
    Returns:
        str: Language code (e.g., 'en', 'es', 'fr')
    """
    if not text or len(text.strip()) < 10:
        return 'en'  # Default to English for short texts
    
    text_lower = text.lower()
    
    # Simple pattern-based detection
    # This is a basic implementation - in production you'd use langdetect or similar
    
    # Spanish indicators
    spanish_patterns = [
        r'\b(el|la|los|las|un|una|de|del|en|con|por|para|que|es|son|está|están)\b',
        r'\b(y|o|pero|si|no|sí|muy|más|menos|todo|todos|todas)\b',
        r'ñ', r'¿', r'¡'
    ]
    
    # French indicators
    french_patterns = [
        r'\b(le|la|les|un|une|de|du|des|et|ou|mais|si|non|très|plus|moins|tout|tous|toutes)\b',
        r'\b(je|tu|il|elle|nous|vous|ils|elles|est|sont|être|avoir)\b',
        r'ç', r'à', r'é', r'è', r'ê', r'ë', r'î', r'ï', r'ô', r'ù', r'û', r'ü', r'ÿ'
    ]
    
    # German indicators
    german_patterns = [
        r'\b(der|die|das|ein|eine|und|oder|aber|wenn|nicht|sehr|mehr|weniger|alle)\b',
        r'\b(ich|du|er|sie|es|wir|ihr|sie|ist|sind|sein|haben)\b',
        r'ä', r'ö', r'ü', r'ß'
    ]
    
    # Italian indicators
    italian_patterns = [
        r'\b(il|la|lo|gli|le|un|una|di|del|della|e|o|ma|se|non|molto|più|meno|tutto|tutti|tutte)\b',
        r'\b(io|tu|lui|lei|noi|voi|loro|è|sono|essere|avere)\b'
    ]
    
    # Portuguese indicators
    portuguese_patterns = [
        r'\b(o|a|os|as|um|uma|de|do|da|dos|das|e|ou|mas|se|não|muito|mais|menos|todo|todos|todas)\b',
        r'\b(eu|tu|ele|ela|nós|vós|eles|elas|é|são|ser|ter)\b',
        r'ã', r'õ', r'ç'
    ]
    
    # Count matches for each language
    scores = {
        'es': sum(len(re.findall(pattern, text_lower)) for pattern in spanish_patterns),
        'fr': sum(len(re.findall(pattern, text_lower)) for pattern in french_patterns),
        'de': sum(len(re.findall(pattern, text_lower)) for pattern in german_patterns),
        'it': sum(len(re.findall(pattern, text_lower)) for pattern in italian_patterns),
        'pt': sum(len(re.findall(pattern, text_lower)) for pattern in portuguese_patterns)
    }
    
    # English indicators (check last to avoid false positives)
    english_patterns = [
        r'\b(the|a|an|and|or|but|if|not|very|more|less|all|some|any)\b',
        r'\b(i|you|he|she|it|we|they|is|are|was|were|be|have|has|had)\b',
        r'\b(this|that|these|those|what|where|when|why|how)\b'
    ]
    
    scores['en'] = sum(len(re.findall(pattern, text_lower)) for pattern in english_patterns)
    
    # Find the language with the highest score
    if max(scores.values()) == 0:
        return 'en'  # Default to English if no patterns match
    
    detected_lang = max(scores, key=scores.get)
    
    # If English score is very close to the top score, prefer English
    if scores['en'] > 0 and scores[detected_lang] - scores['en'] < 2:
        detected_lang = 'en'
    
    logger.info(f"Language detection scores: {scores}, detected: {detected_lang}")
    
    return detected_lang

def get_language_name(lang_code):
    """
    Get the full language name from language code.
    
    Args:
        lang_code (str): Language code
        
    Returns:
        str: Full language name
    """
    language_names = {
        'en': 'English',
        'es': 'Spanish',
        'fr': 'French',
        'de': 'German',
        'it': 'Italian',
        'pt': 'Portuguese',
        'zh': 'Chinese',
        'ja': 'Japanese',
        'ko': 'Korean',
        'ar': 'Arabic',
        'ru': 'Russian',
        'hi': 'Hindi'
    }
    
    return language_names.get(lang_code, lang_code.upper())
