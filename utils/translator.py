"""
Multilingual translation utilities for fake news detection
"""

import logging
import time
from typing import Dict, Optional, List
import re

try:
    from googletrans import Translator, LANGUAGES
    GOOGLETRANS_AVAILABLE = True
except ImportError:
    GOOGLETRANS_AVAILABLE = False
    LANGUAGES = {}

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultilingualTranslator:
    """
    Multilingual translation service for preprocessing text before fake news detection.
    """
    
    def __init__(self, target_language: str = "en"):
        """
        Initialize the translator.
        
        Args:
            target_language: Target language code (default: 'en' for English)
        """
        self.target_language = target_language
        self.translator = None
        self.is_available = GOOGLETRANS_AVAILABLE
        
        # Language detection patterns (basic heuristics)
        self.language_patterns = {
            'es': re.compile(r'\b(el|la|los|las|un|una|de|en|con|por|para|que|es|son|está|están)\b', re.IGNORECASE),
            'fr': re.compile(r'\b(le|la|les|un|une|de|du|des|en|avec|pour|que|est|sont|être|avoir)\b', re.IGNORECASE),
            'de': re.compile(r'\b(der|die|das|ein|eine|und|oder|mit|für|von|zu|ist|sind|haben|sein)\b', re.IGNORECASE),
            'it': re.compile(r'\b(il|la|lo|gli|le|un|una|di|da|in|con|per|che|è|sono|essere|avere)\b', re.IGNORECASE),
            'pt': re.compile(r'\b(o|a|os|as|um|uma|de|em|com|por|para|que|é|são|estar|ter)\b', re.IGNORECASE),
            'ru': re.compile(r'[а-яё]', re.IGNORECASE),
            'ar': re.compile(r'[ا-ي]'),
            'zh': re.compile(r'[一-龯]'),
            'ja': re.compile(r'[ひらがなカタカナ一-龯]'),
            'ko': re.compile(r'[가-힣]'),
            'hi': re.compile(r'[अ-ह]'),
        }
        
        if self.is_available:
            self._initialize_translator()
    
    def _initialize_translator(self):
        """Initialize the Google Translator."""
        try:
            self.translator = Translator()
            logger.info("Google Translator initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Google Translator: {str(e)}")
            self.is_available = False
    
    def detect_language(self, text: str) -> Dict:
        """
        Detect the language of the input text.
        
        Args:
            text: Input text
            
        Returns:
            Dictionary with detection results
        """
        if not text or not isinstance(text, str):
            return {
                "language": "unknown",
                "confidence": 0.0,
                "method": "none",
                "error": "Invalid input text"
            }
        
        # Try Google Translate detection first
        if self.is_available and self.translator:
            try:
                detection = self.translator.detect(text)
                return {
                    "language": detection.lang,
                    "confidence": detection.confidence,
                    "method": "google_translate",
                    "error": None
                }
            except Exception as e:
                logger.warning(f"Google Translate detection failed: {str(e)}")
        
        # Fallback to pattern-based detection
        return self._pattern_based_detection(text)
    
    def _pattern_based_detection(self, text: str) -> Dict:
        """
        Detect language using pattern matching (fallback method).
        
        Args:
            text: Input text
            
        Returns:
            Dictionary with detection results
        """
        text_sample = text[:500]  # Use first 500 characters for detection
        scores = {}
        
        # Check for non-Latin scripts first
        for lang, pattern in self.language_patterns.items():
            matches = len(pattern.findall(text_sample))
            if matches > 0:
                scores[lang] = matches / len(text_sample.split())
        
        if scores:
            best_lang = max(scores, key=scores.get)
            confidence = min(scores[best_lang], 1.0)
            
            return {
                "language": best_lang,
                "confidence": confidence,
                "method": "pattern_matching",
                "error": None
            }
        
        # Default to English if no patterns match
        return {
            "language": "en",
            "confidence": 0.5,
            "method": "default",
            "error": None
        }
    
    def translate_text(self, text: str, source_lang: str = "auto", target_lang: str = None) -> Dict:
        """
        Translate text to target language.
        
        Args:
            text: Text to translate
            source_lang: Source language code ('auto' for auto-detection)
            target_lang: Target language code (uses instance default if None)
            
        Returns:
            Dictionary with translation results
        """
        if not text or not isinstance(text, str):
            return {
                "translated_text": "",
                "original_text": text,
                "source_language": "unknown",
                "target_language": target_lang or self.target_language,
                "success": False,
                "error": "Invalid input text",
                "method": "none"
            }
        
        if target_lang is None:
            target_lang = self.target_language
        
        # If source and target are the same, no translation needed
        if source_lang == target_lang and source_lang != "auto":
            return {
                "translated_text": text,
                "original_text": text,
                "source_language": source_lang,
                "target_language": target_lang,
                "success": True,
                "error": None,
                "method": "no_translation_needed"
            }
        
        if not self.is_available:
            return {
                "translated_text": text,  # Return original text if translation unavailable
                "original_text": text,
                "source_language": "unknown",
                "target_language": target_lang,
                "success": False,
                "error": "Translation service not available",
                "method": "unavailable"
            }
        
        try:
            logger.info(f"Translating text from {source_lang} to {target_lang}")
            
            # Translate using Google Translate
            result = self.translator.translate(
                text,
                src=source_lang,
                dest=target_lang
            )
            
            return {
                "translated_text": result.text,
                "original_text": text,
                "source_language": result.src,
                "target_language": target_lang,
                "success": True,
                "error": None,
                "method": "google_translate"
            }
            
        except Exception as e:
            logger.error(f"Translation failed: {str(e)}")
            return {
                "translated_text": text,  # Return original text on failure
                "original_text": text,
                "source_language": source_lang,
                "target_language": target_lang,
                "success": False,
                "error": str(e),
                "method": "google_translate"
            }
    
    def translate_to_english(self, text: str) -> Dict:
        """
        Convenience method to translate text to English.
        
        Args:
            text: Text to translate
            
        Returns:
            Dictionary with translation results
        """
        return self.translate_text(text, source_lang="auto", target_lang="en")
    
    def preprocess_for_detection(self, text: str, auto_translate: bool = True) -> Dict:
        """
        Preprocess text for fake news detection, including optional translation.
        
        Args:
            text: Input text
            auto_translate: Whether to automatically translate non-English text
            
        Returns:
            Dictionary with preprocessing results
        """
        if not text or not isinstance(text, str):
            return {
                "processed_text": "",
                "original_text": text,
                "language_detected": "unknown",
                "translation_performed": False,
                "success": False,
                "error": "Invalid input text"
            }
        
        try:
            # Detect language
            detection_result = self.detect_language(text)
            detected_lang = detection_result.get("language", "unknown")
            
            processed_text = text
            translation_performed = False
            
            # Translate if needed and requested
            if auto_translate and detected_lang != "en" and detected_lang != "unknown":
                translation_result = self.translate_to_english(text)
                
                if translation_result.get("success", False):
                    processed_text = translation_result["translated_text"]
                    translation_performed = True
                    logger.info(f"Translated text from {detected_lang} to English")
                else:
                    logger.warning(f"Translation failed, using original text")
            
            return {
                "processed_text": processed_text,
                "original_text": text,
                "language_detected": detected_lang,
                "language_confidence": detection_result.get("confidence", 0.0),
                "translation_performed": translation_performed,
                "success": True,
                "error": None
            }
            
        except Exception as e:
            logger.error(f"Preprocessing failed: {str(e)}")
            return {
                "processed_text": text,
                "original_text": text,
                "language_detected": "unknown",
                "translation_performed": False,
                "success": False,
                "error": str(e)
            }
    
    def get_supported_languages(self) -> Dict:
        """
        Get list of supported languages.
        
        Returns:
            Dictionary with supported languages
        """
        if self.is_available:
            return {
                "available": True,
                "languages": LANGUAGES,
                "count": len(LANGUAGES)
            }
        else:
            return {
                "available": False,
                "languages": {},
                "count": 0,
                "error": "Translation service not available"
            }
    
    def get_service_info(self) -> Dict:
        """
        Get information about the translation service.
        
        Returns:
            Dictionary with service information
        """
        return {
            "service": "Google Translate" if self.is_available else "None",
            "available": self.is_available,
            "target_language": self.target_language,
            "pattern_detection_languages": list(self.language_patterns.keys())
        }

# Global translator instance
_global_translator = None

def get_translator(target_language: str = "en") -> MultilingualTranslator:
    """
    Get a global translator instance (singleton pattern).
    
    Args:
        target_language: Target language code
        
    Returns:
        MultilingualTranslator instance
    """
    global _global_translator
    
    if _global_translator is None:
        _global_translator = MultilingualTranslator(target_language=target_language)
    
    return _global_translator
