["tests/test_api.py::test_404_error", "tests/test_api.py::test_405_error", "tests/test_api.py::test_api_health_endpoint", "tests/test_api.py::test_api_info_endpoint", "tests/test_api.py::test_api_with_auto_translate_false", "tests/test_api.py::test_detect_empty_filename", "tests/test_api.py::test_detect_no_file", "tests/test_api.py::test_detect_text_file", "tests/test_api.py::test_detect_unsupported_file_type", "tests/test_api.py::test_detect_with_mocked_components", "tests/test_api.py::test_health_endpoint", "tests/test_api.py::test_home_endpoint", "tests/test_api.py::test_large_file_upload"]