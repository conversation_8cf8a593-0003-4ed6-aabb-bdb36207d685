#!/usr/bin/env python3
"""
Example script to test the Fake News Detection API
"""

import requests
import json
import os
import sys

# API base URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint."""
    print("Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed: {e}")
    print()

def test_api_info():
    """Test the API info endpoint."""
    print("Testing API info...")
    try:
        response = requests.get(f"{BASE_URL}/api/info")
        if response.status_code == 200:
            print("✅ API info retrieved successfully")
            data = response.json()
            print(f"Model: {data.get('model', {}).get('name', 'Unknown')}")
            print(f"Status: {data.get('status', 'Unknown')}")
        else:
            print(f"❌ API info failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ API info failed: {e}")
    print()

def test_text_file_detection(file_path):
    """Test fake news detection with a text file."""
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    print(f"Testing text file detection: {file_path}")
    try:
        with open(file_path, 'rb') as f:
            files = {'file': (os.path.basename(file_path), f, 'text/plain')}
            data = {'type': 'document'}
            
            response = requests.post(f"{BASE_URL}/api/detect", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Detection successful")
                print(f"Prediction: {result.get('prediction', 'Unknown')}")
                print(f"Confidence: {result.get('confidence', 0):.2f}")
                print(f"Language: {result.get('language_detected', 'Unknown')}")
                print(f"Processing time: {result.get('processing_time', {}).get('total', 0):.3f}s")
                
                # Show extracted text preview
                extracted_text = result.get('extracted_text', '')
                if extracted_text:
                    preview = extracted_text[:100] + "..." if len(extracted_text) > 100 else extracted_text
                    print(f"Text preview: {preview}")
            else:
                print(f"❌ Detection failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data.get('error', 'Unknown error')}")
                except:
                    print(f"Response: {response.text}")
                    
    except requests.exceptions.RequestException as e:
        print(f"❌ Detection failed: {e}")
    print()

def test_invalid_file():
    """Test with an invalid file type."""
    print("Testing invalid file type...")
    try:
        # Create a fake executable file
        fake_content = b"MZ\x90\x00"  # PE header signature
        files = {'file': ('malicious.exe', fake_content, 'application/octet-stream')}
        data = {'type': 'document'}
        
        response = requests.post(f"{BASE_URL}/api/detect", files=files, data=data)
        
        if response.status_code == 400:
            print("✅ Invalid file correctly rejected")
            error_data = response.json()
            print(f"Error message: {error_data.get('error', 'Unknown')}")
        else:
            print(f"❌ Invalid file not rejected: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Test failed: {e}")
    print()

def test_large_file():
    """Test with a file that's too large."""
    print("Testing large file upload...")
    try:
        # Create a large file content (20MB)
        large_content = b"x" * (20 * 1024 * 1024)
        files = {'file': ('large_file.txt', large_content, 'text/plain')}
        data = {'type': 'document'}
        
        response = requests.post(f"{BASE_URL}/api/detect", files=files, data=data)
        
        if response.status_code == 413:
            print("✅ Large file correctly rejected")
            error_data = response.json()
            print(f"Error message: {error_data.get('error', 'Unknown')}")
        else:
            print(f"❌ Large file not rejected: {response.status_code}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Test failed: {e}")
    print()

def main():
    """Run all tests."""
    print("🚀 Starting API Tests")
    print("=" * 50)
    
    # Basic connectivity tests
    test_health_check()
    test_api_info()
    
    # File detection tests
    sample_files_dir = os.path.join(os.path.dirname(__file__), '..', 'sample_files')
    
    # Test with real news sample
    real_news_file = os.path.join(sample_files_dir, 'sample_news.txt')
    test_text_file_detection(real_news_file)
    
    # Test with fake news sample
    fake_news_file = os.path.join(sample_files_dir, 'fake_news_sample.txt')
    test_text_file_detection(fake_news_file)
    
    # Error handling tests
    test_invalid_file()
    test_large_file()
    
    print("🏁 Tests completed!")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        BASE_URL = sys.argv[1]
        print(f"Using custom API URL: {BASE_URL}")
    
    main()
