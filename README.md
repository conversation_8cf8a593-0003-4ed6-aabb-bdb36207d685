# Multilingual Fake News Detection System

A comprehensive Flask-based API for detecting fake news from multiple input sources including text, images, documents, and audio files. Built with multilingual RoBERTa model and supporting various text extraction methods.

## 🌟 Features

- 🤖 **RoBERTa-based Detection**: Uses multilingual RoBERTa model for accurate fake news detection
- 🖼️ **Image Text Extraction**: OCR support using Tesseract and OpenCV
- 📄 **Document Processing**: Extract text from PDF, DOC, DOCX files
- 🎤 **Audio Transcription**: Speech-to-text using Whisper (configurable)
- 🌍 **Multilingual Support**: Automatic translation and language detection
- 🚀 **REST API**: Easy-to-use Flask API with comprehensive endpoints
- ✅ **Robust Error Handling**: Comprehensive validation and error management
- 🔒 **Security Features**: File validation, size limits, and malware detection
- 🐳 **Docker Support**: Ready-to-deploy Docker containers
- 📊 **Comprehensive Testing**: Full test suite with pytest
- 📈 **Production Ready**: Gunicorn configuration and deployment scripts

## 🚀 Quick Start

### Method 1: Using the Deployment Script (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd snsSemProject

# Make deployment script executable
chmod +x deploy.sh

# Run in development mode
./deploy.sh --environment development

# Or run in production mode
./deploy.sh --environment production --port 8000 --workers 4
```

### Method 2: Docker Deployment

```bash
# Clone the repository
git clone <repository-url>
cd snsSemProject

# Deploy with Docker
./deploy.sh --environment docker

# Or manually with docker-compose
docker-compose up -d
```

### Method 3: Manual Setup

```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy and configure environment
cp .env.example .env
# Edit .env file with your configurations

# Run the application
python app.py
```

The API will be available at `http://localhost:5000`

## API Endpoints

### Health Check
```bash
GET /health
GET /api/health
```

### System Information
```bash
GET /api/info
```

### Fake News Detection
```bash
POST /api/detect
Content-Type: multipart/form-data

Parameters:
- file: The file to analyze (image/document/audio)
- type: Input type (image/document/audio/auto)
```

## 📖 Usage Examples

### Using cURL

#### Detect from Text File
```bash
curl -X POST http://localhost:8000/api/detect \
  -F "file=@sample_files/sample_news.txt" \
  -F "type=document"
```

#### Detect from Image with OCR
```bash
curl -X POST http://localhost:5000/api/detect \
  -F "file=@news_screenshot.png" \
  -F "type=image"
```

#### Detect from PDF Document
```bash
curl -X POST http://localhost:5000/api/detect \
  -F "file=@news_article.pdf" \
  -F "type=document"
```

#### Detect from Audio (when configured)
```bash
curl -X POST http://localhost:5000/api/detect \
  -F "file=@news_audio.mp3" \
  -F "type=audio"
```

#### Auto-detect File Type
```bash
curl -X POST http://localhost:8000/api/detect \
  -F "file=@unknown_file.pdf" \
  -F "type=auto"
```

#### Disable Auto-translation
```bash
curl -X POST http://localhost:8000/api/detect \
  -F "file=@spanish_news.txt" \
  -F "type=document" \
  -F "auto_translate=false"
```

### Using Python

```python
import requests

# Test with a text file
with open('sample_files/sample_news.txt', 'rb') as f:
    files = {'file': ('news.txt', f, 'text/plain')}
    data = {'type': 'document'}

    response = requests.post('http://localhost:8000/api/detect',
                           files=files, data=data)

    if response.status_code == 200:
        result = response.json()
        print(f"Prediction: {result['prediction']}")
        print(f"Confidence: {result['confidence']:.2f}")
        print(f"Language: {result['language_detected']}")
    else:
        print(f"Error: {response.json()}")
```

### Using the Test Script

```bash
# Run comprehensive API tests
python examples/test_api.py

# Test against a different server
python examples/test_api.py http://your-server:8000
```

## Supported File Formats

- **Images**: PNG, JPG, JPEG, GIF
- **Documents**: PDF, DOC, DOCX, TXT
- **Audio**: MP3, WAV, M4A

## Project Structure

```
snsSemProject/
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── requirements.txt      # Python dependencies
├── routes/              # API route handlers
│   ├── main.py         # Main routes
│   └── api.py          # API endpoints
├── models/             # ML models and detection logic
├── utils/              # Utility functions
├── tests/              # Test files
└── temp/               # Temporary file storage
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file from the template:

```bash
cp .env.example .env
```

Key configuration options:

| Variable | Description | Default |
|----------|-------------|---------|
| `FLASK_ENV` | Environment (development/production) | `development` |
| `SECRET_KEY` | Flask secret key | `dev-secret-key` |
| `MODEL_NAME` | HuggingFace model name | `cardiffnlp/twitter-roberta-base-sentiment-latest` |
| `DEVICE` | Computation device (cpu/cuda) | `cpu` |
| `MAX_CONTENT_LENGTH` | Max file size in bytes | `16777216` (16MB) |
| `AUTO_TRANSLATE` | Enable auto-translation | `true` |
| `UPLOAD_FOLDER` | Temporary upload directory | `temp` |

### Model Configuration

The system uses RoBERTa-based models from Hugging Face. You can configure different models by setting the `MODEL_NAME` environment variable:

```bash
# For multilingual support
MODEL_NAME=cardiffnlp/twitter-xlm-roberta-base-sentiment-latest

# For English-only (faster)
MODEL_NAME=cardiffnlp/twitter-roberta-base-sentiment-latest

# For custom models
MODEL_NAME=your-username/your-custom-model
```

## 🧪 Development

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-flask

# Run all tests
pytest tests/ -v

# Run specific test file
pytest tests/test_api.py -v

# Run with coverage
pytest tests/ --cov=. --cov-report=html
```

### Development Mode

```bash
# Set environment
export FLASK_ENV=development
export FLASK_DEBUG=True

# Run with auto-reload
python app.py

# Or use the deployment script
./deploy.sh --environment development
```

### Code Quality

```bash
# Install development dependencies
pip install black flake8 mypy

# Format code
black .

# Check code style
flake8 .

# Type checking
mypy .
```

## 🚀 Deployment

### Production Deployment

```bash
# Using the deployment script
./deploy.sh --environment production --port 8000 --workers 4

# Manual deployment with Gunicorn
gunicorn --config gunicorn.conf.py app:create_app
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Scale the service
docker-compose up -d --scale fake-news-detector=3

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Cloud Deployment

The application is ready for deployment on various cloud platforms:

- **Heroku**: Use the included `Procfile` and `runtime.txt`
- **AWS ECS**: Use the Docker configuration
- **Google Cloud Run**: Deploy the Docker container
- **Azure Container Instances**: Use docker-compose configuration

## 📊 Performance

### Benchmarks

- **Text Processing**: ~100-500ms per document
- **Image OCR**: ~1-3 seconds per image
- **Model Inference**: ~50-200ms per prediction
- **Memory Usage**: ~500MB-2GB (depending on model)

### Optimization Tips

1. **Use GPU**: Set `DEVICE=cuda` for faster inference
2. **Batch Processing**: Process multiple files together
3. **Model Caching**: Models are cached after first load
4. **File Size Limits**: Configure appropriate limits for your use case

## 🔒 Security

### File Validation

- File type validation based on MIME types
- File size limits to prevent DoS attacks
- Malware signature detection
- Path traversal protection
- Executable file blocking

### API Security

- Input sanitization
- Rate limiting (configurable)
- CORS protection
- Security headers
- Error message sanitization

## 🐛 Troubleshooting

### Common Issues

1. **Model Loading Errors**
   ```bash
   # Check if model exists on Hugging Face
   # Verify internet connection
   # Check disk space for model cache
   ```

2. **OCR Not Working**
   ```bash
   # Install Tesseract
   sudo apt-get install tesseract-ocr
   # Or on macOS
   brew install tesseract
   ```

3. **Memory Issues**
   ```bash
   # Reduce model size or use CPU
   export DEVICE=cpu
   # Increase system memory or use swap
   ```

4. **Port Already in Use**
   ```bash
   # Change port in configuration
   ./deploy.sh --port 8080
   ```

### Logs

```bash
# View application logs
tail -f logs/error.log
tail -f logs/access.log

# Docker logs
docker-compose logs -f fake-news-detector
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Clone your fork
git clone https://github.com/your-username/multilingual-fake-news-detector.git
cd multilingual-fake-news-detector

# Install development dependencies
pip install -r requirements.txt
pip install -e .

# Run tests
pytest tests/

# Submit your changes
```

## 📄 License

MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Hugging Face](https://huggingface.co/) for the transformer models
- [Cardiff NLP](https://github.com/cardiffnlp) for the RoBERTa models
- [Tesseract OCR](https://github.com/tesseract-ocr/tesseract) for text extraction
- [Flask](https://flask.palletsprojects.com/) for the web framework

## 📞 Support

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/multilingual-fake-news-detector/issues)
- 📖 Documentation: [Wiki](https://github.com/your-username/multilingual-fake-news-detector/wiki)
- 💬 Discussions: [GitHub Discussions](https://github.com/your-username/multilingual-fake-news-detector/discussions)
