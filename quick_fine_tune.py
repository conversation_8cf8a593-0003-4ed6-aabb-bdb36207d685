#!/usr/bin/env python3
"""
Quick fine-tuning with just 200 articles (100 real + 100 fake)
"""

import pandas as pd
import torch
import numpy as np
import logging
import os
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from transformers import (
    RobertaTokenizer, 
    RobertaForSequenceClassification,
    get_linear_schedule_with_warmup
)
from torch.optim import AdamW
from torch.utils.data import TensorDataset, DataLoader
import time

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_small_dataset():
    """Load just 100 real + 100 fake articles."""
    logger.info("Loading small dataset (100 real + 100 fake)...")
    
    # Load datasets
    real_df = pd.read_csv('True.csv').head(100)  # First 100 real
    fake_df = pd.read_csv('Fake.csv').head(100)  # First 100 fake
    
    # Add labels
    real_df['label'] = 0  # Real news
    fake_df['label'] = 1  # Fake news
    
    # Combine datasets
    df = pd.concat([real_df, fake_df], ignore_index=True)
    
    # Combine title and text
    df['text'] = df['title'].fillna('') + " " + df['text'].fillna('')
    
    # Keep only text and label
    df = df[['text', 'label']].copy()
    df.dropna(inplace=True)
    
    # Shuffle
    df = df.sample(frac=1, random_state=42).reset_index(drop=True)
    
    logger.info(f"Dataset size: {len(df)} articles")
    logger.info(f"Real news: {len(df[df['label'] == 0])}")
    logger.info(f"Fake news: {len(df[df['label'] == 1])}")
    
    return df

def prepare_data(df, tokenizer, max_length=256):
    """Prepare data for training."""
    logger.info("Preparing data...")
    
    # Split data: 70% train, 15% val, 15% test
    train_df, temp_df = train_test_split(df, test_size=0.3, random_state=42, stratify=df['label'])
    val_df, test_df = train_test_split(temp_df, test_size=0.5, random_state=42, stratify=temp_df['label'])
    
    logger.info(f"Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")
    
    def create_data_loader(data_df, batch_size=8):
        # Tokenize
        encoded = tokenizer(
            data_df['text'].tolist(),
            add_special_tokens=True,
            max_length=max_length,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )
        
        # Create dataset
        dataset = TensorDataset(
            encoded['input_ids'],
            encoded['attention_mask'],
            torch.tensor(data_df['label'].values, dtype=torch.long)
        )
        
        return DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    train_loader = create_data_loader(train_df, batch_size=8)
    val_loader = create_data_loader(val_df, batch_size=8)
    test_loader = create_data_loader(test_df, batch_size=8)
    
    return train_loader, val_loader, test_loader, test_df

def train_model(model, train_loader, val_loader, device, epochs=2):
    """Train the model."""
    logger.info(f"Training for {epochs} epochs...")
    
    optimizer = AdamW(model.parameters(), lr=2e-5)
    
    model.train()
    
    for epoch in range(epochs):
        logger.info(f"\nEpoch {epoch + 1}/{epochs}")
        
        total_loss = 0
        correct_predictions = 0
        total_predictions = 0
        
        for step, batch in enumerate(train_loader):
            input_ids = batch[0].to(device)
            attention_mask = batch[1].to(device)
            labels = batch[2].to(device)
            
            model.zero_grad()
            
            outputs = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs.loss
            logits = outputs.logits
            
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            
            # Calculate accuracy
            predictions = torch.argmax(logits, dim=-1)
            correct_predictions += (predictions == labels).sum().item()
            total_predictions += labels.size(0)
            
            if step % 5 == 0:
                logger.info(f"Step {step}, Loss: {loss.item():.4f}")
        
        avg_loss = total_loss / len(train_loader)
        accuracy = correct_predictions / total_predictions
        
        logger.info(f"Epoch {epoch + 1} - Loss: {avg_loss:.4f}, Accuracy: {accuracy:.4f}")
        
        # Quick validation
        model.eval()
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for batch in val_loader:
                input_ids = batch[0].to(device)
                attention_mask = batch[1].to(device)
                labels = batch[2].to(device)
                
                outputs = model(input_ids=input_ids, attention_mask=attention_mask)
                predictions = torch.argmax(outputs.logits, dim=-1)
                
                val_correct += (predictions == labels).sum().item()
                val_total += labels.size(0)
        
        val_accuracy = val_correct / val_total
        logger.info(f"Validation Accuracy: {val_accuracy:.4f}")
        
        model.train()

def evaluate_model(model, test_loader, device, test_df):
    """Evaluate the model."""
    logger.info("Evaluating model...")
    
    model.eval()
    predictions = []
    true_labels = []
    
    with torch.no_grad():
        for batch in test_loader:
            input_ids = batch[0].to(device)
            attention_mask = batch[1].to(device)
            labels = batch[2].to(device)
            
            outputs = model(input_ids=input_ids, attention_mask=attention_mask)
            batch_predictions = torch.argmax(outputs.logits, dim=-1)
            
            predictions.extend(batch_predictions.cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
    
    accuracy = accuracy_score(true_labels, predictions)
    report = classification_report(true_labels, predictions, target_names=['Real', 'Fake'])
    
    logger.info(f"Test Accuracy: {accuracy:.4f}")
    logger.info(f"\nClassification Report:\n{report}")
    
    return accuracy, predictions, true_labels

def main():
    """Main function."""
    logger.info("🚀 Quick Fine-Tuning for Fake News Detection")
    logger.info("=" * 60)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Load small dataset
    df = load_small_dataset()
    
    # Initialize tokenizer and model
    logger.info("Loading RoBERTa model...")
    tokenizer = RobertaTokenizer.from_pretrained("roberta-base")
    model = RobertaForSequenceClassification.from_pretrained("roberta-base", num_labels=2)
    model.to(device)
    
    # Prepare data
    train_loader, val_loader, test_loader, test_df = prepare_data(df, tokenizer)
    
    # Train model
    start_time = time.time()
    train_model(model, train_loader, val_loader, device, epochs=2)
    training_time = time.time() - start_time
    
    # Evaluate model
    accuracy, predictions, true_labels = evaluate_model(model, test_loader, device, test_df)
    
    # Save model
    save_path = "./quick_fake_news_model"
    logger.info(f"Saving model to {save_path}...")
    
    os.makedirs(save_path, exist_ok=True)
    model.save_pretrained(save_path)
    tokenizer.save_pretrained(save_path)
    
    # Save results
    results = {
        'accuracy': accuracy,
        'training_time': training_time,
        'dataset_size': len(df),
        'model_path': save_path
    }
    
    import json
    with open(os.path.join(save_path, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info("\n" + "=" * 60)
    logger.info("🎉 QUICK FINE-TUNING COMPLETED!")
    logger.info("=" * 60)
    logger.info(f"✅ Model saved to: {save_path}")
    logger.info(f"✅ Test Accuracy: {accuracy:.4f}")
    logger.info(f"✅ Training Time: {training_time:.1f} seconds")
    logger.info(f"✅ Dataset Size: {len(df)} articles")
    
    logger.info("\n🔧 To use this model in your Flask app:")
    logger.info(f"Update MODEL_NAME in config.py to: '{os.path.abspath(save_path)}'")
    
    return save_path

if __name__ == "__main__":
    main()
