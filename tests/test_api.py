"""
Tests for the Flask API endpoints
"""

import pytest
import json
import io
from unittest.mock import patch, MagicMock

def test_home_endpoint(client):
    """Test the home endpoint."""
    response = client.get('/')
    assert response.status_code == 200
    
    data = json.loads(response.data)
    assert 'message' in data
    assert 'endpoints' in data
    assert 'supported_formats' in data
    assert data['message'] == 'Multilingual Fake News Detection API'

def test_health_endpoint(client):
    """Test the health check endpoint."""
    response = client.get('/health')
    assert response.status_code == 200
    
    data = json.loads(response.data)
    assert data['status'] == 'healthy'

def test_api_health_endpoint(client):
    """Test the API health check endpoint."""
    response = client.get('/api/health')
    assert response.status_code == 200
    
    data = json.loads(response.data)
    assert data['status'] == 'healthy'
    assert 'timestamp' in data

def test_api_info_endpoint(client):
    """Test the API info endpoint."""
    response = client.get('/api/info')
    assert response.status_code == 200
    
    data = json.loads(response.data)
    assert 'model' in data
    assert 'text_extraction' in data
    assert 'translation' in data
    assert 'api' in data
    assert data['status'] == 'operational'

def test_detect_no_file(client):
    """Test detection endpoint with no file."""
    response = client.post('/api/detect')
    assert response.status_code == 400
    
    data = json.loads(response.data)
    assert 'error' in data
    assert data['error'] == 'No file provided'

def test_detect_empty_filename(client):
    """Test detection endpoint with empty filename."""
    data = {'file': (io.BytesIO(b''), '')}
    response = client.post('/api/detect', data=data)
    assert response.status_code == 400
    
    response_data = json.loads(response.data)
    assert 'error' in response_data
    assert response_data['error'] == 'No file selected'

def test_detect_unsupported_file_type(client):
    """Test detection endpoint with unsupported file type."""
    data = {'file': (io.BytesIO(b'test content'), 'test.exe')}
    response = client.post('/api/detect', data=data)
    assert response.status_code == 400
    
    response_data = json.loads(response.data)
    assert 'error' in response_data
    assert response_data['error'] == 'File type not supported'

def test_detect_text_file(client, sample_text_file):
    """Test detection endpoint with a text file."""
    with open(sample_text_file, 'rb') as f:
        data = {
            'file': (f, 'test.txt'),
            'type': 'document'
        }
        response = client.post('/api/detect', data=data)
    
    # The response might be 400 if validation fails or 200 if successful
    # Since we don't have the actual model loaded, we expect some kind of response
    assert response.status_code in [200, 400, 500]
    
    response_data = json.loads(response.data)
    # Should have some response structure
    assert isinstance(response_data, dict)

@patch('models.fake_news_detector.get_detector')
@patch('utils.text_extractor.get_extractor')
@patch('utils.translator.get_translator')
@patch('utils.validators.get_validator')
def test_detect_with_mocked_components(mock_validator, mock_translator, mock_extractor, mock_detector, client):
    """Test detection endpoint with mocked components."""
    # Mock validator
    mock_validator_instance = MagicMock()
    mock_validator_instance.sanitize_filename.return_value = 'test.txt'
    mock_validator_instance.validate_file.return_value = {
        'valid': True,
        'file_type': 'document',
        'size': 100,
        'mime_type': 'text/plain'
    }
    mock_validator.return_value = mock_validator_instance
    
    # Mock extractor
    mock_extractor_instance = MagicMock()
    mock_extractor_instance.extract_text.return_value = {
        'success': True,
        'text': 'This is a test news article.',
        'method': 'text_file'
    }
    mock_extractor.return_value = mock_extractor_instance
    
    # Mock translator
    mock_translator_instance = MagicMock()
    mock_translator_instance.preprocess_for_detection.return_value = {
        'processed_text': 'This is a test news article.',
        'language_detected': 'en',
        'language_confidence': 0.9,
        'translation_performed': False,
        'success': True
    }
    mock_translator.return_value = mock_translator_instance
    
    # Mock detector
    mock_detector_instance = MagicMock()
    mock_detector_instance.predict.return_value = {
        'prediction': 'Real',
        'confidence': 0.85,
        'probabilities': {'Real': 0.85, 'Fake': 0.15},
        'processing_time': 0.1
    }
    mock_detector.return_value = mock_detector_instance
    
    # Test the endpoint
    data = {
        'file': (io.BytesIO(b'This is a test news article.'), 'test.txt'),
        'type': 'document'
    }
    response = client.post('/api/detect', data=data)
    
    assert response.status_code == 200
    response_data = json.loads(response.data)
    
    assert response_data['prediction'] == 'Real'
    assert response_data['confidence'] == 0.85
    assert 'probabilities' in response_data
    assert 'extracted_text' in response_data
    assert 'language_detected' in response_data
    assert 'file_info' in response_data

def test_404_error(client):
    """Test 404 error handling."""
    response = client.get('/nonexistent')
    assert response.status_code == 404
    
    data = json.loads(response.data)
    assert data['error'] == 'Not Found'
    assert data['status_code'] == 404

def test_405_error(client):
    """Test 405 method not allowed error."""
    response = client.put('/api/detect')  # PUT not allowed
    assert response.status_code == 405
    
    data = json.loads(response.data)
    assert data['error'] == 'Method Not Allowed'
    assert data['status_code'] == 405

def test_large_file_upload(client):
    """Test file upload size limit."""
    # Create a large file (larger than the limit)
    large_content = b'x' * (20 * 1024 * 1024)  # 20MB
    
    data = {
        'file': (io.BytesIO(large_content), 'large_file.txt'),
        'type': 'document'
    }
    
    response = client.post('/api/detect', data=data)
    
    # Should get a 413 error for file too large
    assert response.status_code == 413

def test_api_with_auto_translate_false(client, sample_text_file):
    """Test API with auto_translate set to false."""
    with open(sample_text_file, 'rb') as f:
        data = {
            'file': (f, 'test.txt'),
            'type': 'document',
            'auto_translate': 'false'
        }
        response = client.post('/api/detect', data=data)
    
    # Should get some response (might be error due to missing model)
    assert response.status_code in [200, 400, 500]
    
    response_data = json.loads(response.data)
    assert isinstance(response_data, dict)
