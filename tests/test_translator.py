"""
Tests for translation utilities
"""

import pytest
from unittest.mock import patch, MagicMock

from utils.translator import MultilingualTranslator, get_translator

def test_translator_initialization():
    """Test MultilingualTranslator initialization."""
    translator = MultilingualTranslator()
    
    assert translator.target_language == "en"
    assert isinstance(translator.language_patterns, dict)
    assert 'es' in translator.language_patterns  # Spanish
    assert 'fr' in translator.language_patterns  # French

def test_translator_custom_target_language():
    """Test translator with custom target language."""
    translator = MultilingualTranslator(target_language="es")
    assert translator.target_language == "es"

def test_pattern_based_detection():
    """Test pattern-based language detection."""
    translator = MultilingualTranslator()
    
    # Test English (should default)
    result = translator._pattern_based_detection("This is an English sentence.")
    assert result['language'] == 'en'
    assert result['method'] == 'default'
    
    # Test Spanish
    result = translator._pattern_based_detection("Esta es una oración en español con palabras como el, la, de, en.")
    assert result['language'] == 'es'
    assert result['method'] == 'pattern_matching'
    
    # Test French
    result = translator._pattern_based_detection("Ceci est une phrase en français avec des mots comme le, la, de, en.")
    assert result['language'] == 'fr'
    assert result['method'] == 'pattern_matching'

@patch('utils.translator.GOOGLETRANS_AVAILABLE', True)
@patch('utils.translator.Translator')
def test_detect_language_with_google(mock_translator_class):
    """Test language detection with Google Translate."""
    # Mock Google Translator
    mock_translator = MagicMock()
    mock_detection = MagicMock()
    mock_detection.lang = 'es'
    mock_detection.confidence = 0.95
    mock_translator.detect.return_value = mock_detection
    mock_translator_class.return_value = mock_translator
    
    translator = MultilingualTranslator()
    translator._initialize_translator()
    
    result = translator.detect_language("Esta es una oración en español.")
    
    assert result['language'] == 'es'
    assert result['confidence'] == 0.95
    assert result['method'] == 'google_translate'

def test_detect_language_invalid_input():
    """Test language detection with invalid input."""
    translator = MultilingualTranslator()
    
    # Test with None
    result = translator.detect_language(None)
    assert result['language'] == 'unknown'
    assert 'error' in result
    
    # Test with empty string
    result = translator.detect_language("")
    assert result['language'] == 'unknown'
    assert 'error' in result

@patch('utils.translator.GOOGLETRANS_AVAILABLE', True)
@patch('utils.translator.Translator')
def test_translate_text_with_google(mock_translator_class):
    """Test text translation with Google Translate."""
    # Mock Google Translator
    mock_translator = MagicMock()
    mock_result = MagicMock()
    mock_result.text = "This is a sentence in English."
    mock_result.src = "es"
    mock_translator.translate.return_value = mock_result
    mock_translator_class.return_value = mock_translator
    
    translator = MultilingualTranslator()
    translator._initialize_translator()
    
    result = translator.translate_text("Esta es una oración en español.", source_lang="es", target_lang="en")
    
    assert result['success']
    assert result['translated_text'] == "This is a sentence in English."
    assert result['source_language'] == "es"
    assert result['target_language'] == "en"
    assert result['method'] == 'google_translate'

def test_translate_text_same_language():
    """Test translation when source and target are the same."""
    translator = MultilingualTranslator()
    
    text = "This is an English sentence."
    result = translator.translate_text(text, source_lang="en", target_lang="en")
    
    assert result['success']
    assert result['translated_text'] == text
    assert result['method'] == 'no_translation_needed'

def test_translate_text_invalid_input():
    """Test translation with invalid input."""
    translator = MultilingualTranslator()
    
    # Test with None
    result = translator.translate_text(None)
    assert not result['success']
    assert 'error' in result
    
    # Test with empty string
    result = translator.translate_text("")
    assert not result['success']
    assert 'error' in result

def test_translate_text_service_unavailable():
    """Test translation when service is unavailable."""
    translator = MultilingualTranslator()
    translator.is_available = False
    
    text = "Esta es una oración en español."
    result = translator.translate_text(text, source_lang="es", target_lang="en")
    
    assert not result['success']
    assert result['translated_text'] == text  # Should return original text
    assert 'not available' in result['error']

def test_translate_to_english():
    """Test convenience method for translating to English."""
    translator = MultilingualTranslator()
    
    # Mock the translate_text method
    with patch.object(translator, 'translate_text') as mock_translate:
        mock_translate.return_value = {'success': True, 'translated_text': 'English text'}
        
        result = translator.translate_to_english("Texto en español")
        
        mock_translate.assert_called_once_with("Texto en español", source_lang="auto", target_lang="en")
        assert result['success']

def test_preprocess_for_detection():
    """Test text preprocessing for detection."""
    translator = MultilingualTranslator()
    
    # Mock the detect_language and translate_to_english methods
    with patch.object(translator, 'detect_language') as mock_detect, \
         patch.object(translator, 'translate_to_english') as mock_translate:
        
        mock_detect.return_value = {
            'language': 'es',
            'confidence': 0.9
        }
        mock_translate.return_value = {
            'success': True,
            'translated_text': 'This is English text.'
        }
        
        result = translator.preprocess_for_detection("Esto es texto en español.", auto_translate=True)
        
        assert result['success']
        assert result['processed_text'] == 'This is English text.'
        assert result['language_detected'] == 'es'
        assert result['translation_performed']

def test_preprocess_for_detection_no_translation():
    """Test preprocessing without translation."""
    translator = MultilingualTranslator()
    
    with patch.object(translator, 'detect_language') as mock_detect:
        mock_detect.return_value = {
            'language': 'en',
            'confidence': 0.9
        }
        
        text = "This is English text."
        result = translator.preprocess_for_detection(text, auto_translate=True)
        
        assert result['success']
        assert result['processed_text'] == text
        assert result['language_detected'] == 'en'
        assert not result['translation_performed']

def test_preprocess_for_detection_auto_translate_false():
    """Test preprocessing with auto_translate disabled."""
    translator = MultilingualTranslator()
    
    with patch.object(translator, 'detect_language') as mock_detect:
        mock_detect.return_value = {
            'language': 'es',
            'confidence': 0.9
        }
        
        text = "Esto es texto en español."
        result = translator.preprocess_for_detection(text, auto_translate=False)
        
        assert result['success']
        assert result['processed_text'] == text
        assert result['language_detected'] == 'es'
        assert not result['translation_performed']

def test_get_supported_languages_available():
    """Test getting supported languages when service is available."""
    translator = MultilingualTranslator()
    translator.is_available = True
    
    result = translator.get_supported_languages()
    
    assert result['available']
    assert 'languages' in result
    assert 'count' in result

def test_get_supported_languages_unavailable():
    """Test getting supported languages when service is unavailable."""
    translator = MultilingualTranslator()
    translator.is_available = False
    
    result = translator.get_supported_languages()
    
    assert not result['available']
    assert result['languages'] == {}
    assert result['count'] == 0

def test_get_service_info():
    """Test getting service information."""
    translator = MultilingualTranslator()
    
    info = translator.get_service_info()
    
    assert 'service' in info
    assert 'available' in info
    assert 'target_language' in info
    assert 'pattern_detection_languages' in info
    
    assert info['target_language'] == translator.target_language

def test_get_translator_singleton():
    """Test the global translator singleton."""
    translator1 = get_translator()
    translator2 = get_translator()
    
    # Should return the same instance
    assert translator1 is translator2

def test_get_translator_with_custom_language():
    """Test getting translator with custom target language."""
    # Reset the global translator
    import utils.translator
    utils.translator._global_translator = None
    
    translator = get_translator(target_language="es")
    
    assert translator.target_language == "es"
