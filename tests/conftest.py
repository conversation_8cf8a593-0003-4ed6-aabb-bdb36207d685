"""
Pytest configuration and fixtures for testing
"""

import pytest
import tempfile
import os
from PIL import Image
import io

from app import create_app

@pytest.fixture
def app():
    """Create and configure a test Flask app."""
    app = create_app('testing')
    
    # Create a temporary directory for uploads during testing
    app.config['UPLOAD_FOLDER'] = tempfile.mkdtemp()
    
    yield app
    
    # Cleanup
    import shutil
    if os.path.exists(app.config['UPLOAD_FOLDER']):
        shutil.rmtree(app.config['UPLOAD_FOLDER'])

@pytest.fixture
def client(app):
    """Create a test client for the Flask app."""
    return app.test_client()

@pytest.fixture
def runner(app):
    """Create a test runner for the Flask app."""
    return app.test_cli_runner()

@pytest.fixture
def sample_text_file():
    """Create a sample text file for testing."""
    content = """
    This is a sample news article for testing the fake news detection system.
    The article contains some text that can be analyzed by the machine learning model.
    This is just a test and not real news content.
    """
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(content)
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    if os.path.exists(temp_path):
        os.unlink(temp_path)

@pytest.fixture
def sample_image_file():
    """Create a sample image file with text for testing OCR."""
    # Create a simple image with text
    img = Image.new('RGB', (400, 200), color='white')
    
    # Convert to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
        f.write(img_bytes.getvalue())
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    if os.path.exists(temp_path):
        os.unlink(temp_path)

@pytest.fixture
def sample_pdf_content():
    """Sample PDF content for testing."""
    return b"""
    %PDF-1.4
    1 0 obj
    <<
    /Type /Catalog
    /Pages 2 0 R
    >>
    endobj
    2 0 obj
    <<
    /Type /Pages
    /Kids [3 0 R]
    /Count 1
    >>
    endobj
    3 0 obj
    <<
    /Type /Page
    /Parent 2 0 R
    /MediaBox [0 0 612 792]
    /Contents 4 0 R
    >>
    endobj
    4 0 obj
    <<
    /Length 44
    >>
    stream
    BT
    /F1 12 Tf
    72 720 Td
    (Sample PDF text for testing) Tj
    ET
    endstream
    endobj
    xref
    0 5
    0000000000 65535 f 
    0000000009 00000 n 
    0000000058 00000 n 
    0000000115 00000 n 
    0000000206 00000 n 
    trailer
    <<
    /Size 5
    /Root 1 0 R
    >>
    startxref
    300
    %%EOF
    """

@pytest.fixture
def sample_pdf_file(sample_pdf_content):
    """Create a sample PDF file for testing."""
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
        f.write(sample_pdf_content)
        temp_path = f.name
    
    yield temp_path
    
    # Cleanup
    if os.path.exists(temp_path):
        os.unlink(temp_path)

@pytest.fixture
def sample_texts():
    """Sample texts in different languages for testing."""
    return {
        'english': "This is a breaking news story about recent developments in technology.",
        'spanish': "Esta es una noticia de última hora sobre desarrollos recientes en tecnología.",
        'french': "Ceci est une nouvelle de dernière minute sur les développements récents en technologie.",
        'german': "Dies ist eine aktuelle Nachricht über jüngste Entwicklungen in der Technologie.",
        'fake_news_sample': "SHOCKING: Scientists discover that water is actually dangerous! Click here to learn more!",
        'real_news_sample': "The Federal Reserve announced today a 0.25% interest rate increase following their monthly meeting."
    }
