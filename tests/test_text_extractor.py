"""
Tests for text extraction utilities
"""

import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock
from PIL import Image
import io

from utils.text_extractor import TextExtractor, get_extractor

def test_text_extractor_initialization():
    """Test TextExtractor initialization."""
    extractor = TextExtractor()
    
    assert '.png' in extractor.supported_image_formats
    assert '.pdf' in extractor.supported_document_formats
    assert '.mp3' in extractor.supported_audio_formats

def test_detect_file_type():
    """Test file type detection."""
    extractor = TextExtractor()
    
    assert extractor._detect_file_type('test.png') == 'image'
    assert extractor._detect_file_type('test.pdf') == 'document'
    assert extractor._detect_file_type('test.mp3') == 'audio'
    assert extractor._detect_file_type('test.unknown') == 'unknown'

def test_extract_text_file_not_found():
    """Test text extraction with non-existent file."""
    extractor = TextExtractor()
    
    result = extractor.extract_text('nonexistent.txt')
    
    assert not result['success']
    assert 'File not found' in result['error']
    assert result['text'] == ''

def test_extract_from_txt_file(sample_text_file):
    """Test text extraction from text file."""
    extractor = TextExtractor()
    
    result = extractor._extract_from_txt(sample_text_file)
    
    assert result['success']
    assert result['file_type'] == 'document'
    assert result['method'] == 'text_file'
    assert len(result['text']) > 0
    assert 'sample news article' in result['text'].lower()

def test_extract_from_txt_file_encoding():
    """Test text extraction with different encodings."""
    extractor = TextExtractor()
    
    # Create a file with special characters
    content = "This is a test with special characters: café, naïve, résumé"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        temp_path = f.name
    
    try:
        result = extractor._extract_from_txt(temp_path)
        assert result['success']
        assert 'café' in result['text']
    finally:
        os.unlink(temp_path)

@patch('utils.text_extractor.pytesseract.image_to_string')
def test_extract_from_image(mock_ocr, sample_image_file):
    """Test text extraction from image."""
    mock_ocr.return_value = "Sample text from image"
    
    extractor = TextExtractor()
    result = extractor.extract_from_image(sample_image_file)
    
    assert result['success']
    assert result['file_type'] == 'image'
    assert result['method'] == 'tesseract_ocr'
    assert result['text'] == "Sample text from image"

def test_extract_from_image_error():
    """Test image extraction error handling."""
    extractor = TextExtractor()
    
    # Test with non-existent file
    result = extractor.extract_from_image('nonexistent.png')
    
    assert not result['success']
    assert 'error' in result
    assert result['file_type'] == 'image'

@patch('utils.text_extractor.fitz.open')
def test_extract_from_pdf_pymupdf(mock_fitz):
    """Test PDF extraction using PyMuPDF."""
    # Mock PyMuPDF
    mock_doc = MagicMock()
    mock_page = MagicMock()
    mock_page.get_text.return_value = "Sample PDF text content"
    mock_doc.__getitem__.return_value = mock_page
    mock_doc.page_count = 1
    mock_fitz.return_value = mock_doc
    
    extractor = TextExtractor()
    
    # Create a temporary PDF file
    with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as f:
        f.write(b'%PDF-1.4 fake pdf content')
        temp_path = f.name
    
    try:
        result = extractor._extract_from_pdf(temp_path)
        
        assert result['success']
        assert result['file_type'] == 'document'
        assert 'Sample PDF text content' in result['text']
    finally:
        os.unlink(temp_path)

def test_extract_from_document_unsupported():
    """Test document extraction with unsupported format."""
    extractor = TextExtractor()
    
    with tempfile.NamedTemporaryFile(suffix='.unknown', delete=False) as f:
        f.write(b'test content')
        temp_path = f.name
    
    try:
        result = extractor.extract_from_document(temp_path)
        
        assert not result['success']
        assert 'Unsupported document format' in result['error']
    finally:
        os.unlink(temp_path)

def test_extract_from_audio_placeholder():
    """Test audio extraction (placeholder implementation)."""
    extractor = TextExtractor()
    
    with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as f:
        f.write(b'fake audio content')
        temp_path = f.name
    
    try:
        result = extractor.extract_from_audio(temp_path)
        
        assert not result['success']
        assert result['file_type'] == 'audio'
        assert 'not implemented' in result['error'].lower()
    finally:
        os.unlink(temp_path)

def test_clean_text():
    """Test text cleaning functionality."""
    extractor = TextExtractor()
    
    # Test normal text
    text = "This is normal text."
    cleaned = extractor._clean_text(text)
    assert cleaned == text
    
    # Test text with excessive whitespace
    text = "This   has    excessive   whitespace."
    cleaned = extractor._clean_text(text)
    assert cleaned == "This has excessive whitespace."
    
    # Test empty text
    assert extractor._clean_text("") == ""
    assert extractor._clean_text(None) == ""
    
    # Test text with short lines
    text = "Line 1\na\nLine 3\nb\nLine 5"
    cleaned = extractor._clean_text(text)
    # Short lines (length <= 2) should be removed
    assert "Line 1" in cleaned
    assert "Line 3" in cleaned
    assert "Line 5" in cleaned

def test_extract_text_auto_detection(sample_text_file):
    """Test automatic file type detection."""
    extractor = TextExtractor()
    
    result = extractor.extract_text(sample_text_file, file_type="auto")
    
    # Should detect as document type
    assert result['file_type'] == 'document'

def test_extract_text_type_mismatch():
    """Test extraction with wrong file type specified."""
    extractor = TextExtractor()
    
    with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
        f.write(b'test content')
        temp_path = f.name
    
    try:
        # Try to extract as image when it's actually a text file
        result = extractor.extract_text(temp_path, file_type="image")
        
        # Should still work but might not extract text properly
        assert isinstance(result, dict)
        assert 'file_type' in result
    finally:
        os.unlink(temp_path)

def test_get_supported_formats():
    """Test getting supported formats."""
    extractor = TextExtractor()
    formats = extractor.get_supported_formats()
    
    assert 'images' in formats
    assert 'documents' in formats
    assert 'audio' in formats
    
    assert '.png' in formats['images']
    assert '.pdf' in formats['documents']
    assert '.mp3' in formats['audio']

def test_get_extractor_singleton():
    """Test the global extractor singleton."""
    extractor1 = get_extractor()
    extractor2 = get_extractor()
    
    # Should return the same instance
    assert extractor1 is extractor2

def test_extract_text_exception_handling():
    """Test exception handling in extract_text."""
    extractor = TextExtractor()
    
    # Create a file and then delete it to cause an exception during processing
    with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
        f.write(b'test content')
        temp_path = f.name
    
    # Delete the file to cause an error
    os.unlink(temp_path)
    
    result = extractor.extract_text(temp_path)
    
    assert not result['success']
    assert 'error' in result
