"""
Tests for file validation utilities
"""

import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock

from utils.validators import FileValidator, get_validator

def test_file_validator_initialization():
    """Test FileValidator initialization."""
    validator = FileValidator()
    
    assert 'image' in validator.max_file_sizes
    assert 'document' in validator.max_file_sizes
    assert 'audio' in validator.max_file_sizes
    
    assert 'image' in validator.allowed_mime_types
    assert 'document' in validator.allowed_mime_types
    assert 'audio' in validator.allowed_mime_types
    
    assert '.exe' in validator.blocked_extensions

def test_determine_file_type():
    """Test file type determination."""
    validator = FileValidator()
    
    # Test with MIME types
    assert validator._determine_file_type('.png', 'image/png') == 'image'
    assert validator._determine_file_type('.pdf', 'application/pdf') == 'document'
    assert validator._determine_file_type('.mp3', 'audio/mpeg') == 'audio'
    
    # Test with extensions (fallback)
    assert validator._determine_file_type('.jpg', 'unknown/type') == 'image'
    assert validator._determine_file_type('.txt', 'unknown/type') == 'document'
    assert validator._determine_file_type('.wav', 'unknown/type') == 'audio'
    
    # Test unknown
    assert validator._determine_file_type('.unknown', 'unknown/type') == 'unknown'

def test_get_mime_type_fallback():
    """Test MIME type detection fallback."""
    validator = FileValidator()
    validator.magic_available = False
    
    assert validator._get_mime_type('test.pdf') == 'application/pdf'
    assert validator._get_mime_type('test.png') == 'image/png'
    assert validator._get_mime_type('test.mp3') == 'audio/mpeg'
    assert validator._get_mime_type('test.unknown') == 'application/octet-stream'

def test_validate_file_size():
    """Test file size validation."""
    validator = FileValidator()
    
    # Test valid size
    result = validator._validate_file_size(1000, 'image')
    assert result['valid']
    
    # Test oversized file
    result = validator._validate_file_size(20 * 1024 * 1024, 'image')  # 20MB for image
    assert not result['valid']
    assert 'exceeds maximum' in result['error']
    
    # Test empty file
    result = validator._validate_file_size(0, 'image')
    assert not result['valid']
    assert 'empty' in result['error']

def test_validate_mime_type():
    """Test MIME type validation."""
    validator = FileValidator()
    
    # Test valid MIME type
    result = validator._validate_mime_type('image/png', 'image')
    assert result['valid']
    
    # Test invalid MIME type
    result = validator._validate_mime_type('application/pdf', 'image')
    assert not result['valid']
    assert 'not allowed' in result['error']
    
    # Test unknown file type
    result = validator._validate_mime_type('image/png', 'unknown')
    assert not result['valid']
    assert 'Unknown or unsupported' in result['error']

def test_security_scan():
    """Test basic security scanning."""
    validator = FileValidator()
    
    # Create a temporary file with safe content
    with tempfile.NamedTemporaryFile(delete=False) as f:
        f.write(b'This is safe text content.')
        temp_path = f.name
    
    try:
        result = validator._security_scan(temp_path, 'document')
        assert result['valid']
    finally:
        os.unlink(temp_path)

def test_security_scan_executable():
    """Test security scan with executable signature."""
    validator = FileValidator()
    
    # Create a file with executable signature
    with tempfile.NamedTemporaryFile(delete=False) as f:
        f.write(b'MZ' + b'fake executable content')  # PE signature
        temp_path = f.name
    
    try:
        result = validator._security_scan(temp_path, 'document')
        assert not result['valid']
        assert 'executable' in result['error']
    finally:
        os.unlink(temp_path)

def test_security_scan_script_in_document():
    """Test security scan for scripts in documents."""
    validator = FileValidator()
    
    # Create a file with script content
    with tempfile.NamedTemporaryFile(delete=False) as f:
        f.write(b'<script>alert("malicious")</script>')
        temp_path = f.name
    
    try:
        result = validator._security_scan(temp_path, 'document')
        assert not result['valid']
        assert 'malicious scripts' in result['error']
    finally:
        os.unlink(temp_path)

def test_validate_file_nonexistent():
    """Test validation of non-existent file."""
    validator = FileValidator()
    
    result = validator.validate_file('nonexistent.txt')
    
    assert not result['valid']
    assert 'does not exist' in result['error']

def test_validate_file_blocked_extension():
    """Test validation of blocked file extension."""
    validator = FileValidator()
    
    # Create a temporary file with blocked extension
    with tempfile.NamedTemporaryFile(suffix='.exe', delete=False) as f:
        f.write(b'fake executable')
        temp_path = f.name
    
    try:
        result = validator.validate_file(temp_path)
        
        assert not result['valid']
        assert 'not allowed for security reasons' in result['error']
    finally:
        os.unlink(temp_path)

def test_validate_file_success():
    """Test successful file validation."""
    validator = FileValidator()
    
    # Create a valid text file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write('This is a valid text file for testing.')
        temp_path = f.name
    
    try:
        result = validator.validate_file(temp_path, 'document')
        
        assert result['valid']
        assert result['file_type'] == 'document'
        assert result['size'] > 0
        assert '.txt' in result.get('extension', '')
    finally:
        os.unlink(temp_path)

def test_validate_file_type_mismatch():
    """Test validation with file type mismatch."""
    validator = FileValidator()
    
    # Create a text file but expect image
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write('This is text content.')
        temp_path = f.name
    
    try:
        result = validator.validate_file(temp_path, 'image')
        
        assert not result['valid']
        assert 'type mismatch' in result['error']
    finally:
        os.unlink(temp_path)

def test_sanitize_filename():
    """Test filename sanitization."""
    validator = FileValidator()
    
    # Test normal filename
    assert validator.sanitize_filename('normal_file.txt') == 'normal_file.txt'
    
    # Test filename with dangerous characters
    dangerous = 'file<>:"/\\|?*.txt'
    sanitized = validator.sanitize_filename(dangerous)
    assert '<' not in sanitized
    assert '>' not in sanitized
    assert ':' not in sanitized
    assert '"' not in sanitized
    
    # Test empty filename
    assert validator.sanitize_filename('') == 'unnamed_file'
    assert validator.sanitize_filename(None) == 'unnamed_file'
    
    # Test filename with only dots and spaces
    assert validator.sanitize_filename('...   ') == 'unnamed_file'
    
    # Test very long filename
    long_name = 'a' * 300 + '.txt'
    sanitized = validator.sanitize_filename(long_name)
    assert len(sanitized) <= 255
    assert sanitized.endswith('.txt')

def test_sanitize_filename_path_traversal():
    """Test filename sanitization against path traversal."""
    validator = FileValidator()
    
    # Test path traversal attempts
    malicious_names = [
        '../../../etc/passwd',
        '..\\..\\windows\\system32\\config',
        '/etc/passwd',
        'C:\\Windows\\System32\\config'
    ]
    
    for name in malicious_names:
        sanitized = validator.sanitize_filename(name)
        assert '..' not in sanitized
        assert '/' not in sanitized
        assert '\\' not in sanitized

def test_get_validator_singleton():
    """Test the global validator singleton."""
    validator1 = get_validator()
    validator2 = get_validator()
    
    # Should return the same instance
    assert validator1 is validator2

def test_validate_file_exception_handling():
    """Test exception handling in file validation."""
    validator = FileValidator()
    
    # Create a file and then make it inaccessible
    with tempfile.NamedTemporaryFile(delete=False) as f:
        f.write(b'test content')
        temp_path = f.name
    
    try:
        # Change permissions to make file inaccessible (on Unix systems)
        if hasattr(os, 'chmod'):
            os.chmod(temp_path, 0o000)
        
        result = validator.validate_file(temp_path)
        
        # Should handle the exception gracefully
        assert not result['valid']
        assert 'error' in result
    finally:
        # Restore permissions and cleanup
        if hasattr(os, 'chmod'):
            os.chmod(temp_path, 0o644)
        os.unlink(temp_path)

@patch('utils.validators.MAGIC_AVAILABLE', True)
@patch('utils.validators.magic.from_file')
def test_get_mime_type_with_magic(mock_magic):
    """Test MIME type detection with python-magic."""
    mock_magic.return_value = 'image/png'
    
    validator = FileValidator()
    validator.magic_available = True
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
        f.write(b'fake png content')
        temp_path = f.name
    
    try:
        mime_type = validator._get_mime_type(temp_path)
        assert mime_type == 'image/png'
        mock_magic.assert_called_once_with(temp_path, mime=True)
    finally:
        os.unlink(temp_path)
