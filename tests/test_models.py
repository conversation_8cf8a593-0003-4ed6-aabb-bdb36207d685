"""
Tests for the fake news detection model
"""

import pytest
from unittest.mock import patch, MagicMock
from models.fake_news_detector import Fake<PERSON>ewsDetector, get_detector

def test_fake_news_detector_initialization():
    """Test FakeNewsDetector initialization."""
    detector = FakeNewsDetector()
    
    assert detector.model_name == "cardiffnlp/twitter-xlm-roberta-base-sentiment-analysis-irony"
    assert detector.device == "cpu"
    assert not detector.is_loaded
    assert detector.label_mapping == {0: "Real", 1: "Fake"}

def test_fake_news_detector_custom_model():
    """Test FakeNewsDetector with custom model name."""
    custom_model = "custom/model-name"
    detector = FakeNewsDetector(model_name=custom_model, device="cuda")
    
    assert detector.model_name == custom_model
    assert detector.device == "cuda"

def test_preprocess_text():
    """Test text preprocessing."""
    detector = FakeNewsDetector()
    
    # Test normal text
    text = "This is a normal news article."
    processed = detector.preprocess_text(text)
    assert processed == text
    
    # Test text with excessive whitespace
    text_with_spaces = "This   is    a   text   with   spaces."
    processed = detector.preprocess_text(text_with_spaces)
    assert processed == "This is a text with spaces."
    
    # Test empty text
    assert detector.preprocess_text("") == ""
    assert detector.preprocess_text(None) == ""
    
    # Test very long text
    long_text = "word " * 1000
    processed = detector.preprocess_text(long_text)
    assert len(processed) <= 2003  # 2000 chars + "..."

@patch('models.fake_news_detector.AutoTokenizer')
@patch('models.fake_news_detector.AutoModelForSequenceClassification')
def test_load_model(mock_model, mock_tokenizer):
    """Test model loading."""
    # Mock the tokenizer and model
    mock_tokenizer_instance = MagicMock()
    mock_model_instance = MagicMock()
    
    mock_tokenizer.from_pretrained.return_value = mock_tokenizer_instance
    mock_model.from_pretrained.return_value = mock_model_instance
    
    detector = FakeNewsDetector()
    detector.load_model()
    
    assert detector.is_loaded
    assert detector.tokenizer == mock_tokenizer_instance
    assert detector.model == mock_model_instance
    
    # Verify the model was moved to the correct device and set to eval mode
    mock_model_instance.to.assert_called_with("cpu")
    mock_model_instance.eval.assert_called_once()

def test_predict_invalid_input():
    """Test prediction with invalid input."""
    detector = FakeNewsDetector()
    
    # Test with None
    result = detector.predict(None)
    assert not result.get('prediction') or result['prediction'] == 'Unknown'
    assert 'error' in result
    
    # Test with empty string
    result = detector.predict("")
    assert not result.get('prediction') or result['prediction'] == 'Unknown'
    assert 'error' in result
    
    # Test with non-string input
    result = detector.predict(123)
    assert not result.get('prediction') or result['prediction'] == 'Unknown'
    assert 'error' in result

@patch('models.fake_news_detector.AutoTokenizer')
@patch('models.fake_news_detector.AutoModelForSequenceClassification')
@patch('models.fake_news_detector.torch')
def test_predict_with_mocked_model(mock_torch, mock_model_class, mock_tokenizer_class):
    """Test prediction with mocked model."""
    # Setup mocks
    mock_tokenizer = MagicMock()
    mock_model = MagicMock()
    mock_outputs = MagicMock()
    mock_logits = MagicMock()
    
    mock_tokenizer_class.from_pretrained.return_value = mock_tokenizer
    mock_model_class.from_pretrained.return_value = mock_model
    
    # Mock tokenizer output
    mock_tokenizer.return_value = {
        'input_ids': MagicMock(),
        'attention_mask': MagicMock()
    }
    
    # Mock model output
    mock_outputs.logits = mock_logits
    mock_model.return_value = mock_outputs
    
    # Mock torch operations
    mock_probabilities = MagicMock()
    mock_probabilities.__getitem__.return_value.item.return_value = 0.85
    mock_torch.nn.functional.softmax.return_value = mock_probabilities
    mock_torch.argmax.return_value.item.return_value = 0
    
    # Create detector and load model
    detector = FakeNewsDetector()
    detector.load_model()
    
    # Test prediction
    result = detector.predict("This is a test news article.")
    
    assert 'prediction' in result
    assert 'confidence' in result
    assert 'probabilities' in result
    assert 'processing_time' in result

def test_predict_batch():
    """Test batch prediction."""
    detector = FakeNewsDetector()
    
    texts = [
        "This is the first news article.",
        "This is the second news article.",
        "This is the third news article."
    ]
    
    results = detector.predict_batch(texts)
    
    assert len(results) == len(texts)
    assert all(isinstance(result, dict) for result in results)

def test_get_model_info():
    """Test getting model information."""
    detector = FakeNewsDetector()
    info = detector.get_model_info()
    
    assert 'model_name' in info
    assert 'device' in info
    assert 'is_loaded' in info
    assert 'label_mapping' in info
    assert 'supported_languages' in info
    
    assert info['model_name'] == detector.model_name
    assert info['device'] == detector.device
    assert info['is_loaded'] == detector.is_loaded

def test_get_detector_singleton():
    """Test the global detector singleton."""
    detector1 = get_detector()
    detector2 = get_detector()
    
    # Should return the same instance
    assert detector1 is detector2

def test_get_detector_with_custom_params():
    """Test getting detector with custom parameters."""
    # Reset the global detector
    import models.fake_news_detector
    models.fake_news_detector._global_detector = None
    
    detector = get_detector(model_name="custom/model", device="cuda")
    
    assert detector.model_name == "custom/model"
    assert detector.device == "cuda"
