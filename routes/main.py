"""
Main routes for the application
"""

from flask import Blueprint, render_template, jsonify

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Home page with web interface."""
    return render_template('index.html')

@main_bp.route('/api')
def api_info():
    """API information endpoint."""
    return jsonify({
        'message': 'Multilingual Fake News Detection API',
        'version': '1.0.0',
        'endpoints': {
            'detect': '/api/detect - POST - Upload file for fake news detection',
            'health': '/api/health - GET - Health check',
            'info': '/api/info - GET - Model and system information'
        },
        'supported_formats': {
            'images': ['png', 'jpg', 'jpeg', 'gif'],
            'documents': ['pdf', 'doc', 'docx', 'txt'],
            'audio': ['mp3', 'wav', 'm4a']
        }
    })

@main_bp.route('/health')
def health():
    """Health check endpoint."""
    return jsonify({'status': 'healthy', 'service': 'fake-news-detection'})

@main_bp.route('/fine-tuning-results')
def fine_tuning_results():
    """Display fine-tuning results."""
    import json
    import os

    try:
        results_path = './simulated_fine_tuned_model/fine_tuning_results.json'
        if os.path.exists(results_path):
            with open(results_path, 'r') as f:
                results = json.load(f)
            return jsonify(results)
        else:
            return jsonify({'error': 'Fine-tuning results not found. Run simulate_fine_tuning.py first.'})
    except Exception as e:
        return jsonify({'error': str(e)})
