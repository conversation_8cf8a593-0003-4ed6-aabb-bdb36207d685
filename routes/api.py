"""
API routes for fake news detection
"""

from flask import Blueprint, request, jsonify, current_app
import os
import time
from werkzeug.utils import secure_filename

# Import our modules
from models.fake_news_detector import get_detector
from utils.text_extractor import get_extractor
from utils.translator import get_translator
from utils.validators import get_validator

api_bp = Blueprint('api', __name__)

def allowed_file(filename):
    """Check if file extension is allowed."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

@api_bp.route('/health', methods=['GET'])
def health():
    """API health check."""
    return jsonify({
        'status': 'healthy',
        'timestamp': time.time(),
        'service': 'fake-news-detection-api'
    })

@api_bp.route('/info', methods=['GET'])
def info():
    """Get model and system information."""
    try:
        # Get component information
        extractor = get_extractor()
        translator = get_translator()
        detector = get_detector()

        return jsonify({
            'model': {
                'name': current_app.config.get('MODEL_NAME', 'Not configured'),
                'device': current_app.config.get('DEVICE', 'cpu'),
                'info': detector.get_model_info()
            },
            'text_extraction': {
                'supported_formats': extractor.get_supported_formats(),
                'methods': ['tesseract_ocr', 'pdfplumber', 'pymupdf', 'python_docx']
            },
            'translation': {
                'service_info': translator.get_service_info(),
                'supported_languages': translator.get_supported_languages()
            },
            'api': {
                'max_file_size': current_app.config.get('MAX_CONTENT_LENGTH', 16777216),
                'supported_extensions': list(current_app.config.get('ALLOWED_EXTENSIONS', [])),
                'auto_translate': current_app.config.get('AUTO_TRANSLATE', True)
            },
            'version': '1.0.0',
            'status': 'operational'
        })

    except Exception as e:
        return jsonify({
            'error': str(e),
            'status': 'error'
        }), 500

@api_bp.route('/detect', methods=['POST'])
def detect_fake_news():
    """
    Main endpoint for fake news detection.
    Accepts file uploads and returns prediction results.
    """
    start_time = time.time()

    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Check file type
        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not supported'}), 400

        # Get input type from form data
        input_type = request.form.get('type', 'auto')
        auto_translate = request.form.get('auto_translate', 'true').lower() == 'true'

        # Initialize validator
        validator = get_validator()

        # Sanitize filename
        sanitized_filename = validator.sanitize_filename(file.filename)
        timestamp = str(int(time.time()))
        temp_filename = f"{timestamp}_{sanitized_filename}"
        filepath = os.path.join(current_app.config['UPLOAD_FOLDER'], temp_filename)

        # Save file temporarily
        file.save(filepath)

        try:
            # Validate uploaded file
            validation_result = validator.validate_file(filepath, input_type)

            if not validation_result.get('valid', False):
                return jsonify({
                    'error': 'File validation failed',
                    'details': validation_result.get('error', 'Unknown validation error'),
                    'file_info': {
                        'filename': sanitized_filename,
                        'original_filename': file.filename,
                        'size': validation_result.get('size', 0),
                        'detected_type': validation_result.get('file_type', 'unknown')
                    }
                }), 400

            # Initialize components
            extractor = get_extractor()
            translator = get_translator()
            detector = get_detector(
                model_name=current_app.config.get('MODEL_NAME'),
                device=current_app.config.get('DEVICE', 'cpu')
            )

            # Step 1: Extract text from file
            extraction_result = extractor.extract_text(filepath, input_type)

            if not extraction_result.get('success', False):
                return jsonify({
                    'error': 'Failed to extract text from file',
                    'details': extraction_result.get('error', 'Unknown error'),
                    'file_info': {
                        'filename': filename,
                        'type': input_type,
                        'size': os.path.getsize(filepath)
                    }
                }), 400

            extracted_text = extraction_result.get('text', '')

            if not extracted_text.strip():
                return jsonify({
                    'error': 'No text found in the uploaded file',
                    'extraction_details': extraction_result,
                    'file_info': {
                        'filename': filename,
                        'type': input_type,
                        'size': os.path.getsize(filepath)
                    }
                }), 400

            # Step 2: Language detection and translation
            preprocessing_result = translator.preprocess_for_detection(
                extracted_text,
                auto_translate=auto_translate
            )

            processed_text = preprocessing_result.get('processed_text', extracted_text)

            # Step 3: Fake news detection
            detection_result = detector.predict(processed_text)

            # Calculate total processing time
            total_time = time.time() - start_time

            # Prepare response
            result = {
                'prediction': detection_result.get('prediction', 'Unknown'),
                'confidence': detection_result.get('confidence', 0.0),
                'probabilities': detection_result.get('probabilities', {}),
                'extracted_text': extracted_text[:500] + '...' if len(extracted_text) > 500 else extracted_text,
                'processed_text': processed_text[:500] + '...' if len(processed_text) > 500 else processed_text,
                'language_detected': preprocessing_result.get('language_detected', 'unknown'),
                'language_confidence': preprocessing_result.get('language_confidence', 0.0),
                'translation_performed': preprocessing_result.get('translation_performed', False),
                'processing_time': {
                    'total': round(total_time, 3),
                    'detection': round(detection_result.get('processing_time', 0), 3)
                },
                'file_info': {
                    'filename': sanitized_filename,
                    'original_filename': file.filename,
                    'type': validation_result.get('file_type', input_type),
                    'size': validation_result.get('size', 0),
                    'mime_type': validation_result.get('mime_type', 'unknown'),
                    'extraction_method': extraction_result.get('method', 'unknown')
                },
                'text_stats': {
                    'original_length': len(extracted_text),
                    'processed_length': len(processed_text),
                    'extraction_success': extraction_result.get('success', False)
                }
            }

            return jsonify(result)

        finally:
            # Clean up temporary file
            if os.path.exists(filepath):
                os.remove(filepath)

    except Exception as e:
        total_time = time.time() - start_time
        return jsonify({
            'error': str(e),
            'processing_time': round(total_time, 3)
        }), 500
