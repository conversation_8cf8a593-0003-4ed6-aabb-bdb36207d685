"""
Multilingual RoBERTa-based Fake News Detection Model
"""

import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import logging
import time
from typing import Dict, List, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FakeNewsDetector:
    """
    Multilingual fake news detection using RoBERTa model.
    """
    
    def __init__(self, model_name: str = "cardiffnlp/twitter-roberta-base-sentiment-latest", device: str = "cpu"):
        """
        Initialize the fake news detector.
        
        Args:
            model_name: HuggingFace model name
            device: Device to run the model on ('cpu' or 'cuda')
        """
        self.model_name = model_name
        self.device = device
        self.tokenizer = None
        self.model = None
        self.is_loaded = False
        
        # Label mappings - adapt based on the actual model
        # For sentiment models: 0=negative, 1=neutral, 2=positive
        # We'll map: negative->Fake, neutral->Unknown, positive->Real
        self.label_mapping = {
            0: "Fake",    # Negative sentiment -> Fake news
            1: "Unknown", # Neutral sentiment -> Unknown
            2: "Real"     # Positive sentiment -> Real news
        }
        
    def load_model(self):
        """Load the tokenizer and model."""
        try:
            logger.info(f"Loading model: {self.model_name}")
            start_time = time.time()
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # Load model
            self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
            self.model.to(self.device)
            self.model.eval()
            
            load_time = time.time() - start_time
            logger.info(f"Model loaded successfully in {load_time:.2f} seconds")
            self.is_loaded = True
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text for better model performance.
        
        Args:
            text: Input text
            
        Returns:
            Preprocessed text
        """
        if not text or not isinstance(text, str):
            return ""
        
        # Basic text cleaning
        text = text.strip()
        
        # Remove excessive whitespace
        text = ' '.join(text.split())
        
        # Truncate if too long (model has token limits)
        max_chars = 2000  # Rough estimate for token limit
        if len(text) > max_chars:
            text = text[:max_chars] + "..."
            
        return text

    def _detect_fake_patterns(self, text: str) -> Dict:
        """
        Detect patterns commonly found in fake news.

        Args:
            text: Text to analyze

        Returns:
            Dictionary with fake news indicators
        """
        text_lower = text.lower()
        fake_score = 0.0
        indicators = []

        # Sensational language patterns
        sensational_words = [
            'shocking', 'urgent', 'breaking', 'exclusive', 'secret', 'exposed',
            'revealed', 'truth', 'conspiracy', 'cover-up', 'scandal', 'bombshell',
            'unbelievable', 'incredible', 'amazing', 'stunning', 'outrageous'
        ]

        # Exaggerated claims
        exaggerated_phrases = [
            '100%', 'everyone', 'nobody', 'always', 'never', 'all', 'every single',
            'completely', 'totally', 'absolutely', 'definitely will', 'guaranteed'
        ]

        # Emotional manipulation
        emotional_words = [
            'terrifying', 'horrifying', 'devastating', 'catastrophic', 'disaster',
            'crisis', 'emergency', 'panic', 'fear', 'scared', 'worried'
        ]

        # Fake authority patterns
        fake_authority = [
            'scientists say', 'experts claim', 'studies show', 'research proves',
            'doctors recommend', 'government hides', 'they don\'t want you to know'
        ]

        # Check for sensational language
        sensational_count = sum(1 for word in sensational_words if word in text_lower)
        if sensational_count > 0:
            fake_score += min(0.3, sensational_count * 0.1)
            indicators.append(f"Sensational language ({sensational_count} instances)")

        # Check for exaggerated claims
        exaggerated_count = sum(1 for phrase in exaggerated_phrases if phrase in text_lower)
        if exaggerated_count > 0:
            fake_score += min(0.4, exaggerated_count * 0.15)
            indicators.append(f"Exaggerated claims ({exaggerated_count} instances)")

        # Check for emotional manipulation
        emotional_count = sum(1 for word in emotional_words if word in text_lower)
        if emotional_count > 0:
            fake_score += min(0.2, emotional_count * 0.1)
            indicators.append(f"Emotional manipulation ({emotional_count} instances)")

        # Check for fake authority
        authority_count = sum(1 for phrase in fake_authority if phrase in text_lower)
        if authority_count > 0:
            fake_score += min(0.3, authority_count * 0.2)
            indicators.append(f"Questionable authority claims ({authority_count} instances)")

        # Check for obvious satire/fake patterns
        obvious_fake = [
            'institute of obvious research', 'dr. fake', 'fake mc', 'obviously fake',
            'this is satire', 'parody', 'joke', 'humor'
        ]

        obvious_count = sum(1 for phrase in obvious_fake if phrase in text_lower)
        if obvious_count > 0:
            fake_score += 0.8  # Very high score for obvious fake content
            indicators.append(f"Obviously fake content detected ({obvious_count} instances)")

        # Cap the score at 1.0
        fake_score = min(1.0, fake_score)

        return {
            'score': fake_score,
            'indicators': indicators,
            'confidence': 'high' if fake_score > 0.7 else 'medium' if fake_score > 0.4 else 'low'
        }

    def predict(self, text: str) -> Dict:
        """
        Predict if the given text is fake news.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dictionary containing prediction results
        """
        if not self.is_loaded:
            self.load_model()
        
        if not text or not isinstance(text, str):
            return {
                "prediction": "Unknown",
                "confidence": 0.0,
                "probabilities": {},
                "error": "Invalid input text"
            }
        
        try:
            start_time = time.time()

            # Preprocess text
            processed_text = self.preprocess_text(text)

            # Detect fake news patterns
            fake_patterns = self._detect_fake_patterns(processed_text)

            # Tokenize
            inputs = self.tokenizer(
                processed_text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )

            # Move to device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            # Predict
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits

                # Apply softmax to get probabilities
                probabilities = F.softmax(logits, dim=-1)

                # Get base prediction
                predicted_class = torch.argmax(probabilities, dim=-1).item()
                base_confidence = probabilities[0][predicted_class].item()

                # Adjust prediction based on fake patterns
                if fake_patterns['score'] > 0.7:  # High fake indicator score
                    predicted_class = 0  # Force to Fake (0 in our mapping)
                    confidence = min(0.95, 0.7 + fake_patterns['score'] * 0.25)
                    final_prediction = "Fake"
                elif fake_patterns['score'] > 0.4:  # Medium fake indicator score
                    if predicted_class == 2:  # If model says Real (positive sentiment), but we have fake indicators
                        # Flip to fake with adjusted confidence
                        predicted_class = 0  # Fake
                        confidence = 0.6 + fake_patterns['score'] * 0.3
                        final_prediction = "Fake"
                    elif predicted_class == 0:  # If model says Fake (negative), increase confidence
                        confidence = min(0.95, base_confidence + fake_patterns['score'] * 0.2)
                        final_prediction = "Fake"
                    else:  # Neutral - lean towards fake if we have indicators
                        predicted_class = 0  # Fake
                        confidence = 0.5 + fake_patterns['score'] * 0.4
                        final_prediction = "Fake"
                else:
                    # Use original model prediction with some adjustments
                    confidence = base_confidence
                    original_prediction = self.label_mapping.get(predicted_class, "Unknown")

                    # For sentiment model, we need to be more careful about mapping
                    if predicted_class == 0:  # Negative sentiment
                        final_prediction = "Fake"
                    elif predicted_class == 2:  # Positive sentiment
                        final_prediction = "Real"
                    else:  # Neutral sentiment
                        # For neutral sentiment, we'll default to Real unless there are indicators
                        final_prediction = "Real"
                        confidence = confidence * 0.7  # Lower confidence for neutral

                # Create adjusted probability dictionary
                prob_dict = {}
                for i, prob in enumerate(probabilities[0]):
                    label = self.label_mapping.get(i, f"Class_{i}")
                    prob_dict[label] = prob.item()

                # Adjust probabilities if we overrode the prediction
                if fake_patterns['score'] > 0.4:
                    prob_dict["Fake"] = confidence
                    prob_dict["Real"] = 1 - confidence
                    # Add other classes if they exist
                    remaining_prob = max(0, 1 - confidence - (1 - confidence))
                    for key in prob_dict:
                        if key not in ["Fake", "Real"]:
                            prob_dict[key] = remaining_prob / max(1, len(prob_dict) - 2)

                prediction_time = time.time() - start_time

                return {
                    "prediction": final_prediction,
                    "confidence": confidence,
                    "probabilities": prob_dict,
                    "processing_time": prediction_time,
                    "text_length": len(text),
                    "processed_text_length": len(processed_text),
                    "fake_indicators": fake_patterns
                }
                
        except Exception as e:
            logger.error(f"Error during prediction: {str(e)}")
            return {
                "prediction": "Error",
                "confidence": 0.0,
                "probabilities": {},
                "error": str(e)
            }
    
    def predict_batch(self, texts: List[str]) -> List[Dict]:
        """
        Predict multiple texts at once.
        
        Args:
            texts: List of texts to analyze
            
        Returns:
            List of prediction results
        """
        if not self.is_loaded:
            self.load_model()
        
        results = []
        for text in texts:
            result = self.predict(text)
            results.append(result)
        
        return results
    
    def get_model_info(self) -> Dict:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_name,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "label_mapping": self.label_mapping,
            "supported_languages": [
                "English", "Spanish", "French", "German", "Italian", 
                "Portuguese", "Arabic", "Hindi", "Chinese", "Dutch",
                "Russian", "Japanese", "Korean"
            ]
        }

# Global model instance for reuse
_global_detector = None

def get_detector(model_name: str = None, device: str = "cpu") -> FakeNewsDetector:
    """
    Get a global detector instance (singleton pattern).
    
    Args:
        model_name: Model name (optional)
        device: Device to use
        
    Returns:
        FakeNewsDetector instance
    """
    global _global_detector
    
    if _global_detector is None:
        if model_name is None:
            model_name = "cardiffnlp/twitter-roberta-base-sentiment-latest"
        _global_detector = FakeNewsDetector(model_name=model_name, device=device)
    
    return _global_detector
