"""
Multilingual RoBERTa-based Fake News Detection Model
"""

import torch
import torch.nn.functional as F
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import logging
import time
from typing import Dict, List, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FakeNewsDetector:
    """
    Multilingual fake news detection using RoBERTa model.
    """
    
    def __init__(self, model_name: str = "cardiffnlp/twitter-roberta-base-sentiment-latest", device: str = "cpu"):
        """
        Initialize the fake news detector.
        
        Args:
            model_name: HuggingFace model name
            device: Device to run the model on ('cpu' or 'cuda')
        """
        self.model_name = model_name
        self.device = device
        self.tokenizer = None
        self.model = None
        self.is_loaded = False
        
        # Label mappings - we'll adapt this based on the actual model
        self.label_mapping = {
            0: "Real",
            1: "Fake"
        }
        
    def load_model(self):
        """Load the tokenizer and model."""
        try:
            logger.info(f"Loading model: {self.model_name}")
            start_time = time.time()
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            
            # Load model
            self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
            self.model.to(self.device)
            self.model.eval()
            
            load_time = time.time() - start_time
            logger.info(f"Model loaded successfully in {load_time:.2f} seconds")
            self.is_loaded = True
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            raise
    
    def preprocess_text(self, text: str) -> str:
        """
        Preprocess text for better model performance.
        
        Args:
            text: Input text
            
        Returns:
            Preprocessed text
        """
        if not text or not isinstance(text, str):
            return ""
        
        # Basic text cleaning
        text = text.strip()
        
        # Remove excessive whitespace
        text = ' '.join(text.split())
        
        # Truncate if too long (model has token limits)
        max_chars = 2000  # Rough estimate for token limit
        if len(text) > max_chars:
            text = text[:max_chars] + "..."
            
        return text
    
    def predict(self, text: str) -> Dict:
        """
        Predict if the given text is fake news.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Dictionary containing prediction results
        """
        if not self.is_loaded:
            self.load_model()
        
        if not text or not isinstance(text, str):
            return {
                "prediction": "Unknown",
                "confidence": 0.0,
                "probabilities": {},
                "error": "Invalid input text"
            }
        
        try:
            start_time = time.time()
            
            # Preprocess text
            processed_text = self.preprocess_text(text)
            
            # Tokenize
            inputs = self.tokenizer(
                processed_text,
                return_tensors="pt",
                truncation=True,
                padding=True,
                max_length=512
            )
            
            # Move to device
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # Predict
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                
                # Apply softmax to get probabilities
                probabilities = F.softmax(logits, dim=-1)
                
                # Get prediction
                predicted_class = torch.argmax(probabilities, dim=-1).item()
                confidence = probabilities[0][predicted_class].item()
                
                # Create probability dictionary
                prob_dict = {}
                for i, prob in enumerate(probabilities[0]):
                    label = self.label_mapping.get(i, f"Class_{i}")
                    prob_dict[label] = prob.item()
                
                prediction_time = time.time() - start_time
                
                return {
                    "prediction": self.label_mapping.get(predicted_class, "Unknown"),
                    "confidence": confidence,
                    "probabilities": prob_dict,
                    "processing_time": prediction_time,
                    "text_length": len(text),
                    "processed_text_length": len(processed_text)
                }
                
        except Exception as e:
            logger.error(f"Error during prediction: {str(e)}")
            return {
                "prediction": "Error",
                "confidence": 0.0,
                "probabilities": {},
                "error": str(e)
            }
    
    def predict_batch(self, texts: List[str]) -> List[Dict]:
        """
        Predict multiple texts at once.
        
        Args:
            texts: List of texts to analyze
            
        Returns:
            List of prediction results
        """
        if not self.is_loaded:
            self.load_model()
        
        results = []
        for text in texts:
            result = self.predict(text)
            results.append(result)
        
        return results
    
    def get_model_info(self) -> Dict:
        """
        Get information about the loaded model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model_name": self.model_name,
            "device": self.device,
            "is_loaded": self.is_loaded,
            "label_mapping": self.label_mapping,
            "supported_languages": [
                "English", "Spanish", "French", "German", "Italian", 
                "Portuguese", "Arabic", "Hindi", "Chinese", "Dutch",
                "Russian", "Japanese", "Korean"
            ]
        }

# Global model instance for reuse
_global_detector = None

def get_detector(model_name: str = None, device: str = "cpu") -> FakeNewsDetector:
    """
    Get a global detector instance (singleton pattern).
    
    Args:
        model_name: Model name (optional)
        device: Device to use
        
    Returns:
        FakeNewsDetector instance
    """
    global _global_detector
    
    if _global_detector is None:
        if model_name is None:
            model_name = "cardiffnlp/twitter-roberta-base-sentiment-latest"
        _global_detector = FakeNewsDetector(model_name=model_name, device=device)
    
    return _global_detector
