"""
Fine-tuning RoBERTa for Fake News Detection using ISOT Dataset
"""

import pandas as pd
import torch
import numpy as np
import logging
import os
from datetime import datetime
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from transformers import (
    <PERSON><PERSON>okenizer,
    RobertaForSequenceClassification,
    get_linear_schedule_with_warmup
)
from torch.optim import AdamW
from torch.utils.data import TensorDataset, DataLoader, RandomSampler, SequentialSampler
import matplotlib.pyplot as plt
import seaborn as sns

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FakeNewsFineTuner:
    """
    Fine-tune RoBERTa model for fake news detection using ISOT dataset.
    """
    
    def __init__(self, model_name="roberta-base", max_length=512, batch_size=16):
        """
        Initialize the fine-tuner.
        
        Args:
            model_name: Pre-trained model name
            max_length: Maximum sequence length
            batch_size: Training batch size
        """
        self.model_name = model_name
        self.max_length = max_length
        self.batch_size = batch_size
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        logger.info(f"Using device: {self.device}")
        logger.info(f"Model: {model_name}")
        logger.info(f"Max length: {max_length}")
        logger.info(f"Batch size: {batch_size}")
        
        # Initialize tokenizer and model
        self.tokenizer = None
        self.model = None
        
        # Training history
        self.training_stats = []
        
    def load_and_preprocess_data(self, true_csv_path="True.csv", fake_csv_path="Fake.csv"):
        """
        Load and preprocess the ISOT dataset.
        
        Args:
            true_csv_path: Path to True.csv file
            fake_csv_path: Path to Fake.csv file
            
        Returns:
            Preprocessed DataFrame
        """
        logger.info("Loading ISOT dataset...")
        
        # Load datasets
        try:
            real_df = pd.read_csv(true_csv_path)
            fake_df = pd.read_csv(fake_csv_path)
            
            logger.info(f"Loaded {len(real_df)} real news articles")
            logger.info(f"Loaded {len(fake_df)} fake news articles")
            
        except Exception as e:
            logger.error(f"Error loading datasets: {str(e)}")
            raise
        
        # Add labels
        real_df['label'] = 0  # Real news
        fake_df['label'] = 1  # Fake news
        
        # Combine datasets
        df = pd.concat([real_df, fake_df], ignore_index=True)
        
        # Combine title and text
        df['text'] = df['title'].fillna('') + " " + df['text'].fillna('')
        
        # Clean and filter
        df = df[['text', 'label']].copy()
        df.dropna(inplace=True)
        
        # Remove very short texts
        df = df[df['text'].str.len() > 50].copy()
        
        # Shuffle the dataset
        df = df.sample(frac=1, random_state=42).reset_index(drop=True)
        
        logger.info(f"Final dataset size: {len(df)} articles")
        logger.info(f"Real news: {len(df[df['label'] == 0])}")
        logger.info(f"Fake news: {len(df[df['label'] == 1])}")
        
        return df
    
    def prepare_data_loaders(self, df, test_size=0.2, val_size=0.1):
        """
        Prepare data loaders for training, validation, and testing.
        
        Args:
            df: Preprocessed DataFrame
            test_size: Test set proportion
            val_size: Validation set proportion
            
        Returns:
            train_loader, val_loader, test_loader
        """
        logger.info("Preparing data loaders...")
        
        # Initialize tokenizer
        self.tokenizer = RobertaTokenizer.from_pretrained(self.model_name)
        
        # Split data
        train_df, test_df = train_test_split(
            df, test_size=test_size, random_state=42, stratify=df['label']
        )
        
        train_df, val_df = train_test_split(
            train_df, test_size=val_size, random_state=42, stratify=train_df['label']
        )
        
        logger.info(f"Train set: {len(train_df)} samples")
        logger.info(f"Validation set: {len(val_df)} samples")
        logger.info(f"Test set: {len(test_df)} samples")
        
        # Tokenize datasets
        train_loader = self._create_data_loader(train_df, is_training=True)
        val_loader = self._create_data_loader(val_df, is_training=False)
        test_loader = self._create_data_loader(test_df, is_training=False)
        
        # Store test data for later evaluation
        self.test_df = test_df
        
        return train_loader, val_loader, test_loader
    
    def _create_data_loader(self, df, is_training=True):
        """Create a data loader from DataFrame."""
        # Tokenize texts
        encoded = self.tokenizer(
            df['text'].tolist(),
            add_special_tokens=True,
            max_length=self.max_length,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt'
        )
        
        # Create dataset
        dataset = TensorDataset(
            encoded['input_ids'],
            encoded['attention_mask'],
            torch.tensor(df['label'].values, dtype=torch.long)
        )
        
        # Create sampler and data loader
        if is_training:
            sampler = RandomSampler(dataset)
        else:
            sampler = SequentialSampler(dataset)
        
        data_loader = DataLoader(
            dataset,
            sampler=sampler,
            batch_size=self.batch_size
        )
        
        return data_loader
    
    def initialize_model(self):
        """Initialize the RoBERTa model for sequence classification."""
        logger.info("Initializing RoBERTa model...")
        
        self.model = RobertaForSequenceClassification.from_pretrained(
            self.model_name,
            num_labels=2,  # Binary classification
            output_attentions=False,
            output_hidden_states=False
        )
        
        self.model.to(self.device)
        
        # Print model info
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        logger.info(f"Total parameters: {total_params:,}")
        logger.info(f"Trainable parameters: {trainable_params:,}")
    
    def train(self, train_loader, val_loader, epochs=3, learning_rate=2e-5, warmup_steps=0):
        """
        Fine-tune the model.
        
        Args:
            train_loader: Training data loader
            val_loader: Validation data loader
            epochs: Number of training epochs
            learning_rate: Learning rate
            warmup_steps: Number of warmup steps
        """
        logger.info(f"Starting training for {epochs} epochs...")
        
        # Initialize optimizer
        optimizer = AdamW(
            self.model.parameters(),
            lr=learning_rate,
            eps=1e-8
        )
        
        # Calculate total training steps
        total_steps = len(train_loader) * epochs
        
        # Initialize scheduler
        scheduler = get_linear_schedule_with_warmup(
            optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=total_steps
        )
        
        # Training loop
        for epoch in range(epochs):
            logger.info(f"\nEpoch {epoch + 1}/{epochs}")
            logger.info("-" * 50)
            
            # Training phase
            train_loss, train_acc = self._train_epoch(train_loader, optimizer, scheduler)
            
            # Validation phase
            val_loss, val_acc = self._validate_epoch(val_loader)
            
            # Store training statistics
            self.training_stats.append({
                'epoch': epoch + 1,
                'train_loss': train_loss,
                'train_acc': train_acc,
                'val_loss': val_loss,
                'val_acc': val_acc
            })
            
            logger.info(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
            logger.info(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        logger.info("Training completed!")
    
    def _train_epoch(self, train_loader, optimizer, scheduler):
        """Train for one epoch."""
        self.model.train()
        
        total_loss = 0
        total_correct = 0
        total_samples = 0
        
        for step, batch in enumerate(train_loader):
            # Move batch to device
            input_ids = batch[0].to(self.device)
            attention_mask = batch[1].to(self.device)
            labels = batch[2].to(self.device)
            
            # Clear gradients
            self.model.zero_grad()
            
            # Forward pass
            outputs = self.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels
            )
            
            loss = outputs.loss
            logits = outputs.logits
            
            # Backward pass
            loss.backward()
            
            # Clip gradients
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            
            # Update parameters
            optimizer.step()
            scheduler.step()
            
            # Calculate accuracy
            predictions = torch.argmax(logits, dim=-1)
            correct = (predictions == labels).sum().item()
            
            total_loss += loss.item()
            total_correct += correct
            total_samples += labels.size(0)
            
            # Print progress
            if step % 100 == 0 and step != 0:
                logger.info(f"Step {step}/{len(train_loader)}, Loss: {loss.item():.4f}")
        
        avg_loss = total_loss / len(train_loader)
        accuracy = total_correct / total_samples
        
        return avg_loss, accuracy
    
    def _validate_epoch(self, val_loader):
        """Validate for one epoch."""
        self.model.eval()
        
        total_loss = 0
        total_correct = 0
        total_samples = 0
        
        with torch.no_grad():
            for batch in val_loader:
                # Move batch to device
                input_ids = batch[0].to(self.device)
                attention_mask = batch[1].to(self.device)
                labels = batch[2].to(self.device)
                
                # Forward pass
                outputs = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels
                )
                
                loss = outputs.loss
                logits = outputs.logits
                
                # Calculate accuracy
                predictions = torch.argmax(logits, dim=-1)
                correct = (predictions == labels).sum().item()
                
                total_loss += loss.item()
                total_correct += correct
                total_samples += labels.size(0)
        
        avg_loss = total_loss / len(val_loader)
        accuracy = total_correct / total_samples
        
        return avg_loss, accuracy
    
    def evaluate(self, test_loader):
        """
        Evaluate the model on test set.
        
        Args:
            test_loader: Test data loader
            
        Returns:
            Dictionary with evaluation metrics
        """
        logger.info("Evaluating model on test set...")
        
        self.model.eval()
        
        predictions = []
        true_labels = []
        
        with torch.no_grad():
            for batch in test_loader:
                # Move batch to device
                input_ids = batch[0].to(self.device)
                attention_mask = batch[1].to(self.device)
                labels = batch[2].to(self.device)
                
                # Forward pass
                outputs = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask
                )
                
                logits = outputs.logits
                batch_predictions = torch.argmax(logits, dim=-1)
                
                predictions.extend(batch_predictions.cpu().numpy())
                true_labels.extend(labels.cpu().numpy())
        
        # Calculate metrics
        accuracy = accuracy_score(true_labels, predictions)
        report = classification_report(true_labels, predictions, output_dict=True)
        
        logger.info(f"Test Accuracy: {accuracy:.4f}")
        logger.info("\nClassification Report:")
        logger.info(classification_report(true_labels, predictions))
        
        return {
            'accuracy': accuracy,
            'predictions': predictions,
            'true_labels': true_labels,
            'classification_report': report
        }
    
    def save_model(self, save_path="./fine_tuned_fake_news_model"):
        """
        Save the fine-tuned model and tokenizer.
        
        Args:
            save_path: Directory to save the model
        """
        logger.info(f"Saving model to {save_path}...")
        
        # Create directory if it doesn't exist
        os.makedirs(save_path, exist_ok=True)
        
        # Save model and tokenizer
        self.model.save_pretrained(save_path)
        self.tokenizer.save_pretrained(save_path)
        
        # Save training statistics
        stats_df = pd.DataFrame(self.training_stats)
        stats_df.to_csv(os.path.join(save_path, 'training_stats.csv'), index=False)
        
        logger.info("Model saved successfully!")
        
        return save_path
    
    def plot_training_history(self, save_path=None):
        """
        Plot training history.
        
        Args:
            save_path: Path to save the plot
        """
        if not self.training_stats:
            logger.warning("No training statistics available")
            return
        
        stats_df = pd.DataFrame(self.training_stats)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # Plot loss
        ax1.plot(stats_df['epoch'], stats_df['train_loss'], 'b-', label='Training Loss')
        ax1.plot(stats_df['epoch'], stats_df['val_loss'], 'r-', label='Validation Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.legend()
        ax1.grid(True)
        
        # Plot accuracy
        ax2.plot(stats_df['epoch'], stats_df['train_acc'], 'b-', label='Training Accuracy')
        ax2.plot(stats_df['epoch'], stats_df['val_acc'], 'r-', label='Validation Accuracy')
        ax2.set_title('Training and Validation Accuracy')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Accuracy')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Training history plot saved to {save_path}")
        
        plt.show()

def main():
    """Main function to run the fine-tuning process."""
    logger.info("Starting RoBERTa Fine-tuning for Fake News Detection")
    logger.info("=" * 60)
    
    # Initialize fine-tuner
    fine_tuner = FakeNewsFineTuner(
        model_name="roberta-base",
        max_length=512,
        batch_size=8 if torch.cuda.is_available() else 4  # Smaller batch for CPU
    )
    
    # Load and preprocess data
    df = fine_tuner.load_and_preprocess_data()
    
    # Prepare data loaders
    train_loader, val_loader, test_loader = fine_tuner.prepare_data_loaders(df)
    
    # Initialize model
    fine_tuner.initialize_model()
    
    # Train the model
    fine_tuner.train(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=3,
        learning_rate=2e-5
    )
    
    # Evaluate the model
    results = fine_tuner.evaluate(test_loader)
    
    # Save the model
    model_path = fine_tuner.save_model()
    
    # Plot training history
    fine_tuner.plot_training_history(
        save_path=os.path.join(model_path, 'training_history.png')
    )
    
    logger.info("Fine-tuning completed successfully!")
    logger.info(f"Model saved to: {model_path}")
    logger.info(f"Test Accuracy: {results['accuracy']:.4f}")

if __name__ == "__main__":
    main()
