#!/usr/bin/env python3
"""
Mini fine-tuning with just 20 articles (10 real + 10 fake)
"""

import pandas as pd
import torch
import logging
import os
import json
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from transformers import <PERSON><PERSON>okenizer, RobertaForSequenceClassification
from torch.optim import AdamW
from torch.utils.data import TensorDataset, DataLoader
import time

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_mini_dataset():
    """Load just 10 real + 10 fake articles."""
    logger.info("Loading mini dataset (10 real + 10 fake)...")
    
    # Load datasets
    real_df = pd.read_csv('True.csv').head(10)  # First 10 real
    fake_df = pd.read_csv('Fake.csv').head(10)  # First 10 fake
    
    # Add labels
    real_df['label'] = 0  # Real news
    fake_df['label'] = 1  # Fake news
    
    # Combine datasets
    df = pd.concat([real_df, fake_df], ignore_index=True)
    
    # Combine title and text (keep it short)
    df['text'] = df['title'].fillna('') + " " + df['text'].fillna('')
    df['text'] = df['text'].str[:500]  # Limit to 500 characters
    
    # Keep only text and label
    df = df[['text', 'label']].copy()
    df.dropna(inplace=True)
    
    # Shuffle
    df = df.sample(frac=1, random_state=42).reset_index(drop=True)
    
    logger.info(f"Dataset size: {len(df)} articles")
    logger.info(f"Real news: {len(df[df['label'] == 0])}")
    logger.info(f"Fake news: {len(df[df['label'] == 1])}")
    
    return df

def quick_train():
    """Super quick training."""
    logger.info("🚀 Mini Fine-Tuning for Fake News Detection")
    logger.info("=" * 50)
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Load mini dataset
    df = load_mini_dataset()
    
    # Initialize tokenizer and model
    logger.info("Loading RoBERTa model...")
    tokenizer = RobertaTokenizer.from_pretrained("roberta-base")
    model = RobertaForSequenceClassification.from_pretrained("roberta-base", num_labels=2)
    model.to(device)
    
    # Prepare data (use 80% for train, 20% for test)
    train_df, test_df = train_test_split(df, test_size=0.2, random_state=42, stratify=df['label'])
    
    logger.info(f"Train: {len(train_df)}, Test: {len(test_df)}")
    
    # Tokenize training data
    train_encoded = tokenizer(
        train_df['text'].tolist(),
        add_special_tokens=True,
        max_length=128,  # Short sequences
        padding='max_length',
        truncation=True,
        return_attention_mask=True,
        return_tensors='pt'
    )
    
    # Create training dataset
    train_dataset = TensorDataset(
        train_encoded['input_ids'],
        train_encoded['attention_mask'],
        torch.tensor(train_df['label'].values, dtype=torch.long)
    )
    
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True)
    
    # Train for just 1 epoch
    logger.info("Training for 1 epoch...")
    optimizer = AdamW(model.parameters(), lr=5e-5)  # Higher learning rate for quick training
    
    model.train()
    start_time = time.time()
    
    total_loss = 0
    for step, batch in enumerate(train_loader):
        input_ids = batch[0].to(device)
        attention_mask = batch[1].to(device)
        labels = batch[2].to(device)
        
        model.zero_grad()
        
        outputs = model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            labels=labels
        )
        
        loss = outputs.loss
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
        logger.info(f"Step {step + 1}, Loss: {loss.item():.4f}")
    
    training_time = time.time() - start_time
    avg_loss = total_loss / len(train_loader)
    
    logger.info(f"Training completed! Avg Loss: {avg_loss:.4f}, Time: {training_time:.1f}s")
    
    # Quick evaluation on test set
    logger.info("Evaluating on test set...")
    
    # Tokenize test data
    test_encoded = tokenizer(
        test_df['text'].tolist(),
        add_special_tokens=True,
        max_length=128,
        padding='max_length',
        truncation=True,
        return_attention_mask=True,
        return_tensors='pt'
    )
    
    model.eval()
    predictions = []
    true_labels = test_df['label'].values
    
    with torch.no_grad():
        outputs = model(
            input_ids=test_encoded['input_ids'].to(device),
            attention_mask=test_encoded['attention_mask'].to(device)
        )
        predictions = torch.argmax(outputs.logits, dim=-1).cpu().numpy()
    
    accuracy = accuracy_score(true_labels, predictions)
    
    # Save model
    save_path = "./mini_fake_news_model"
    logger.info(f"Saving model to {save_path}...")
    
    os.makedirs(save_path, exist_ok=True)
    model.save_pretrained(save_path)
    tokenizer.save_pretrained(save_path)
    
    # Save results and sample data
    results = {
        'accuracy': float(accuracy),
        'training_time': float(training_time),
        'dataset_size': len(df),
        'train_size': len(train_df),
        'test_size': len(test_df),
        'avg_loss': float(avg_loss),
        'model_path': save_path,
        'sample_predictions': [
            {
                'text': test_df.iloc[i]['text'][:100] + "...",
                'true_label': 'Real' if true_labels[i] == 0 else 'Fake',
                'predicted_label': 'Real' if predictions[i] == 0 else 'Fake',
                'correct': bool(true_labels[i] == predictions[i])
            }
            for i in range(len(test_df))
        ]
    }
    
    with open(os.path.join(save_path, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # Save sample training data for frontend
    sample_data = {
        'real_samples': [
            {
                'title': df[df['label'] == 0].iloc[i]['text'].split('.')[0] + "...",
                'text': df[df['label'] == 0].iloc[i]['text'][:200] + "..."
            }
            for i in range(min(5, len(df[df['label'] == 0])))
        ],
        'fake_samples': [
            {
                'title': df[df['label'] == 1].iloc[i]['text'].split('.')[0] + "...",
                'text': df[df['label'] == 1].iloc[i]['text'][:200] + "..."
            }
            for i in range(min(5, len(df[df['label'] == 1])))
        ]
    }
    
    with open(os.path.join(save_path, 'sample_data.json'), 'w') as f:
        json.dump(sample_data, f, indent=2)
    
    logger.info("\n" + "=" * 50)
    logger.info("🎉 MINI FINE-TUNING COMPLETED!")
    logger.info("=" * 50)
    logger.info(f"✅ Model saved to: {save_path}")
    logger.info(f"✅ Test Accuracy: {accuracy:.4f}")
    logger.info(f"✅ Training Time: {training_time:.1f} seconds")
    logger.info(f"✅ Dataset Size: {len(df)} articles")
    
    # Show predictions
    logger.info("\n📊 Test Predictions:")
    for i, (text, true_label, pred_label, correct) in enumerate(zip(
        test_df['text'], true_labels, predictions, true_labels == predictions
    )):
        status = "✅" if correct else "❌"
        true_str = "Real" if true_label == 0 else "Fake"
        pred_str = "Real" if pred_label == 0 else "Fake"
        logger.info(f"{status} {i+1}. True: {true_str}, Predicted: {pred_str}")
        logger.info(f"   Text: {text[:80]}...")
    
    return save_path

if __name__ == "__main__":
    quick_train()
