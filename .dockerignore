# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Logs
*.log
logs/

# Temporary files
temp/
tmp/
*.tmp

# Test files
.pytest_cache/
.coverage
htmlcov/

# Environment files
.env
.env.local
.env.production

# Documentation
docs/
*.md
!README.md

# Docker
Dockerfile*
docker-compose*
.dockerignore
