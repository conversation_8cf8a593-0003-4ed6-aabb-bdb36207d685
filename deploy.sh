#!/bin/bash

# Deployment script for Multilingual Fake News Detection System
# This script helps deploy the application in different environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="development"
PORT=8000
WORKERS=4

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -w|--workers)
            WORKERS="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -e, --environment    Environment (development|production|docker) [default: development]"
            echo "  -p, --port          Port to run on [default: 8000]"
            echo "  -w, --workers       Number of workers [default: 4]"
            echo "  -h, --help          Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "Starting deployment for environment: $ENVIRONMENT"

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p temp logs

case $ENVIRONMENT in
    "development")
        print_status "Setting up development environment..."
        
        # Check if virtual environment exists
        if [ ! -d "venv" ]; then
            print_status "Creating virtual environment..."
            python3 -m venv venv
        fi
        
        # Activate virtual environment
        print_status "Activating virtual environment..."
        source venv/bin/activate
        
        # Install dependencies
        print_status "Installing dependencies..."
        pip install -r requirements.txt
        
        # Copy environment file if it doesn't exist
        if [ ! -f ".env" ]; then
            print_status "Creating .env file from template..."
            cp .env.example .env
            print_warning "Please edit .env file with your configuration"
        fi
        
        # Run the application
        print_status "Starting development server on port $PORT..."
        export FLASK_ENV=development
        python app.py
        ;;
        
    "production")
        print_status "Setting up production environment..."
        
        # Check if virtual environment exists
        if [ ! -d "venv" ]; then
            print_status "Creating virtual environment..."
            python3 -m venv venv
        fi
        
        # Activate virtual environment
        source venv/bin/activate
        
        # Install dependencies
        print_status "Installing production dependencies..."
        pip install -r requirements.txt
        
        # Check for environment file
        if [ ! -f ".env" ]; then
            print_error ".env file not found. Please create it from .env.example"
            exit 1
        fi
        
        # Run with Gunicorn
        print_status "Starting production server with Gunicorn..."
        export FLASK_ENV=production
        gunicorn --config gunicorn.conf.py --bind 0.0.0.0:$PORT --workers $WORKERS app:create_app
        ;;
        
    "docker")
        print_status "Setting up Docker environment..."
        
        # Check if Docker is installed
        if ! command -v docker &> /dev/null; then
            print_error "Docker is not installed. Please install Docker first."
            exit 1
        fi
        
        # Check if docker-compose is installed
        if ! command -v docker-compose &> /dev/null; then
            print_error "docker-compose is not installed. Please install docker-compose first."
            exit 1
        fi
        
        # Build and run with Docker Compose
        print_status "Building Docker image..."
        docker-compose build
        
        print_status "Starting services with Docker Compose..."
        docker-compose up -d
        
        print_status "Checking service health..."
        sleep 10
        docker-compose ps
        
        print_status "Docker deployment completed!"
        print_status "API is available at: http://localhost:$PORT"
        print_status "To view logs: docker-compose logs -f"
        print_status "To stop services: docker-compose down"
        ;;
        
    *)
        print_error "Unknown environment: $ENVIRONMENT"
        print_error "Supported environments: development, production, docker"
        exit 1
        ;;
esac

print_status "Deployment completed successfully!"
print_status "API is available at: http://localhost:$PORT"
print_status "Health check: http://localhost:$PORT/health"
print_status "API info: http://localhost:$PORT/api/info"
