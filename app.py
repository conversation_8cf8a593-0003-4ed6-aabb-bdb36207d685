"""
Multilingual Fake News Detection System
Main Flask application entry point
"""

import os
from flask import Flask
from flask_cors import CORS
from config import config
from utils.error_handlers import register_error_handlers, setup_request_middleware, register_api_exception_handler

def create_app(config_name=None):
    """Application factory pattern."""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Enable CORS for cross-origin requests
    CORS(app)

    # Create upload directory if it doesn't exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

    # Set up error handling and middleware
    register_error_handlers(app)
    register_api_exception_handler(app)
    setup_request_middleware(app)

    # Register blueprints
    from routes.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    from routes.main import main_bp
    app.register_blueprint(main_bp)

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(host='0.0.0.0', port=8000, debug=True)
