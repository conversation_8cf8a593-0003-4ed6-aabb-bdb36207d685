version: '3.8'

services:
  fake-news-detector:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-change-in-production}
      - MODEL_NAME=${MODEL_NAME:-cardiffnlp/twitter-roberta-base-sentiment-latest}
      - DEVICE=${DEVICE:-cpu}
      - MAX_CONTENT_LENGTH=${MAX_CONTENT_LENGTH:-16777216}
      - AUTO_TRANSLATE=${AUTO_TRANSLATE:-true}
    volumes:
      - ./temp:/app/temp
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add Redis for caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   ports:
  #     - "6379:6379"
  #   restart: unless-stopped

  # Optional: Add PostgreSQL for data storage (uncomment if needed)
  # postgres:
  #   image: postgres:15-alpine
  #   environment:
  #     POSTGRES_DB: fake_news_db
  #     POSTGRES_USER: ${POSTGRES_USER:-fake_news_user}
  #     POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-change-this-password}
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   ports:
  #     - "5432:5432"
  #   restart: unless-stopped

# volumes:
#   postgres_data:
