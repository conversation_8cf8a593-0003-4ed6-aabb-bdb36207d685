#!/usr/bin/env python3
"""
Quick test script for fine-tuning setup
"""

import pandas as pd
import torch
import logging
from models.fine_tune_roberta import FakeNewsFineTuner

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_data_loading():
    """Test if we can load the datasets."""
    logger.info("Testing data loading...")
    
    try:
        # Load datasets
        real_df = pd.read_csv('True.csv')
        fake_df = pd.read_csv('Fake.csv')
        
        logger.info(f"Loaded {len(real_df)} real news articles")
        logger.info(f"Loaded {len(fake_df)} fake news articles")
        
        # Show sample data
        logger.info("Sample real news:")
        logger.info(real_df.head(1)[['title', 'text']].iloc[0]['title'])
        
        logger.info("Sample fake news:")
        logger.info(fake_df.head(1)[['title', 'text']].iloc[0]['title'])
        
        return True
        
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        return False

def test_fine_tuner_init():
    """Test fine-tuner initialization."""
    logger.info("Testing fine-tuner initialization...")
    
    try:
        fine_tuner = FakeNewsFineTuner(
            model_name="roberta-base",
            max_length=128,  # Smaller for testing
            batch_size=2     # Very small batch
        )
        
        logger.info(f"Device: {fine_tuner.device}")
        logger.info("Fine-tuner initialized successfully")
        
        return fine_tuner
        
    except Exception as e:
        logger.error(f"Error initializing fine-tuner: {e}")
        return None

def test_data_preprocessing():
    """Test data preprocessing."""
    logger.info("Testing data preprocessing...")
    
    try:
        fine_tuner = FakeNewsFineTuner(batch_size=2)
        
        # Load and preprocess a small sample
        df = fine_tuner.load_and_preprocess_data()
        
        # Use only first 100 samples for testing
        df_sample = df.head(100)
        logger.info(f"Using sample of {len(df_sample)} articles")
        
        # Test data loader creation
        train_loader, val_loader, test_loader = fine_tuner.prepare_data_loaders(df_sample)
        
        logger.info(f"Train batches: {len(train_loader)}")
        logger.info(f"Val batches: {len(val_loader)}")
        logger.info(f"Test batches: {len(test_loader)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error in data preprocessing: {e}")
        logger.error("Full traceback:", exc_info=True)
        return False

def main():
    """Run all tests."""
    logger.info("🚀 Starting Fine-Tuning Tests")
    logger.info("=" * 50)
    
    # Test 1: Data loading
    if not test_data_loading():
        logger.error("❌ Data loading test failed")
        return
    logger.info("✅ Data loading test passed")
    
    # Test 2: Fine-tuner initialization
    fine_tuner = test_fine_tuner_init()
    if fine_tuner is None:
        logger.error("❌ Fine-tuner initialization test failed")
        return
    logger.info("✅ Fine-tuner initialization test passed")
    
    # Test 3: Data preprocessing
    if not test_data_preprocessing():
        logger.error("❌ Data preprocessing test failed")
        return
    logger.info("✅ Data preprocessing test passed")
    
    logger.info("\n🎉 All tests passed! Ready for fine-tuning.")
    logger.info("\nTo run full fine-tuning:")
    logger.info("python fine_tune.py --sample_size 1000 --epochs 1 --batch_size 4")

if __name__ == "__main__":
    main()
