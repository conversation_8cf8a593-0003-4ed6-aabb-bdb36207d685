# API Documentation

## Base URL

```
http://localhost:8000
```

## Authentication

Currently, no authentication is required. For production use, consider implementing API keys or OAuth.

## Rate Limiting

Default rate limit: 100 requests per minute per IP address.

## Content Types

- Request: `multipart/form-data` for file uploads
- Response: `application/json`

## Error Handling

All errors return JSON with the following structure:

```json
{
  "error": "Error message",
  "status_code": 400,
  "details": "Additional error details (optional)"
}
```

## HTTP Status Codes

- `200` - Success
- `400` - Bad Request (invalid input)
- `404` - Not Found
- `405` - Method Not Allowed
- `413` - Request Entity Too Large
- `415` - Unsupported Media Type
- `429` - Too Many Requests
- `500` - Internal Server Error

## Endpoints

### Health Check

#### GET /health

Check if the service is running.

**Response:**
```json
{
  "status": "healthy",
  "service": "fake-news-detection"
}
```

#### GET /api/health

API-specific health check with timestamp.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": **********.0,
  "service": "fake-news-detection-api"
}
```

### System Information

#### GET /api/info

Get detailed information about the system, model, and capabilities.

**Response:**
```json
{
  "model": {
    "name": "cardiffnlp/twitter-roberta-base-sentiment-latest",
    "device": "cpu",
    "info": {
      "model_name": "cardiffnlp/twitter-roberta-base-sentiment-latest",
      "device": "cpu",
      "is_loaded": true,
      "label_mapping": {"0": "Real", "1": "Fake"},
      "supported_languages": ["English", "Spanish", "French", "..."]
    }
  },
  "text_extraction": {
    "supported_formats": {
      "images": [".png", ".jpg", ".jpeg", ".gif", ".bmp"],
      "documents": [".pdf", ".doc", ".docx", ".txt"],
      "audio": [".mp3", ".wav", ".m4a"]
    },
    "methods": ["tesseract_ocr", "pdfplumber", "pymupdf", "python_docx"]
  },
  "translation": {
    "service_info": {
      "service": "Google Translate",
      "available": true,
      "target_language": "en"
    }
  },
  "api": {
    "max_file_size": 16777216,
    "supported_extensions": ["pdf", "doc", "docx", "txt", "png", "jpg", "jpeg", "gif", "mp3", "wav", "m4a"],
    "auto_translate": true
  },
  "version": "1.0.0",
  "status": "operational"
}
```

### Fake News Detection

#### POST /api/detect

Detect fake news from uploaded files.

**Parameters:**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file` | File | Yes | The file to analyze |
| `type` | String | No | File type hint: `image`, `document`, `audio`, `auto` (default: `auto`) |
| `auto_translate` | String | No | Enable auto-translation: `true`, `false` (default: `true`) |

**Supported File Types:**

- **Images**: PNG, JPG, JPEG, GIF, BMP (OCR extraction)
- **Documents**: PDF, DOC, DOCX, TXT (text extraction)
- **Audio**: MP3, WAV, M4A (speech-to-text, when configured)

**Request Example:**

```bash
curl -X POST http://localhost:8000/api/detect \
  -F "file=@sample_news.txt" \
  -F "type=document" \
  -F "auto_translate=true"
```

**Success Response (200):**

```json
{
  "prediction": "Real",
  "confidence": 0.85,
  "probabilities": {
    "Real": 0.85,
    "Fake": 0.15
  },
  "extracted_text": "This is the extracted text from the file...",
  "processed_text": "This is the processed text after translation...",
  "language_detected": "en",
  "language_confidence": 0.95,
  "translation_performed": false,
  "processing_time": {
    "total": 1.234,
    "detection": 0.456
  },
  "file_info": {
    "filename": "sample_news.txt",
    "original_filename": "sample_news.txt",
    "type": "document",
    "size": 1024,
    "mime_type": "text/plain",
    "extraction_method": "text_file"
  },
  "text_stats": {
    "original_length": 500,
    "processed_length": 500,
    "extraction_success": true
  }
}
```

**Error Responses:**

**400 - Bad Request:**
```json
{
  "error": "No file provided",
  "status_code": 400
}
```

**400 - File Validation Failed:**
```json
{
  "error": "File validation failed",
  "details": "File size exceeds maximum allowed size",
  "file_info": {
    "filename": "large_file.pdf",
    "size": 52428800,
    "detected_type": "document"
  },
  "status_code": 400
}
```

**400 - Text Extraction Failed:**
```json
{
  "error": "Failed to extract text from file",
  "details": "No text found in PDF",
  "file_info": {
    "filename": "empty.pdf",
    "type": "document",
    "size": 1024
  },
  "status_code": 400
}
```

**413 - File Too Large:**
```json
{
  "error": "File Too Large",
  "message": "The uploaded file exceeds the maximum allowed size",
  "max_size": 16777216,
  "status_code": 413
}
```

**415 - Unsupported Media Type:**
```json
{
  "error": "Unsupported Media Type",
  "message": "The uploaded file type is not supported",
  "supported_types": ["pdf", "doc", "docx", "txt", "png", "jpg", "jpeg", "gif", "mp3", "wav", "m4a"],
  "status_code": 415
}
```

## Response Fields

### Detection Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `prediction` | String | Prediction result: "Real", "Fake", or "Unknown" |
| `confidence` | Float | Confidence score (0.0 to 1.0) |
| `probabilities` | Object | Probability scores for each class |
| `extracted_text` | String | Text extracted from the file (truncated for display) |
| `processed_text` | String | Text after preprocessing and translation |
| `language_detected` | String | Detected language code (e.g., "en", "es", "fr") |
| `language_confidence` | Float | Language detection confidence (0.0 to 1.0) |
| `translation_performed` | Boolean | Whether translation was performed |
| `processing_time` | Object | Processing time breakdown |
| `file_info` | Object | Information about the uploaded file |
| `text_stats` | Object | Statistics about the extracted text |

### File Info Fields

| Field | Type | Description |
|-------|------|-------------|
| `filename` | String | Sanitized filename |
| `original_filename` | String | Original uploaded filename |
| `type` | String | Detected file type |
| `size` | Integer | File size in bytes |
| `mime_type` | String | MIME type of the file |
| `extraction_method` | String | Method used for text extraction |

## Usage Examples

### Python Client

```python
import requests
import json

def detect_fake_news(file_path, file_type="auto", auto_translate=True):
    """Detect fake news from a file."""
    url = "http://localhost:8000/api/detect"
    
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'type': file_type,
            'auto_translate': str(auto_translate).lower()
        }
        
        response = requests.post(url, files=files, data=data)
        
        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"API Error: {response.status_code} - {response.text}")

# Example usage
try:
    result = detect_fake_news("sample_news.txt", "document")
    print(f"Prediction: {result['prediction']}")
    print(f"Confidence: {result['confidence']:.2f}")
except Exception as e:
    print(f"Error: {e}")
```

### JavaScript Client

```javascript
async function detectFakeNews(file, type = 'auto', autoTranslate = true) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    formData.append('auto_translate', autoTranslate.toString());
    
    try {
        const response = await fetch('http://localhost:8000/api/detect', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            return await response.json();
        } else {
            throw new Error(`API Error: ${response.status}`);
        }
    } catch (error) {
        console.error('Error:', error);
        throw error;
    }
}

// Example usage
const fileInput = document.getElementById('file-input');
const file = fileInput.files[0];

detectFakeNews(file, 'document')
    .then(result => {
        console.log('Prediction:', result.prediction);
        console.log('Confidence:', result.confidence);
    })
    .catch(error => {
        console.error('Error:', error);
    });
```

## Best Practices

1. **File Size**: Keep files under 16MB for optimal performance
2. **File Types**: Use supported formats for best results
3. **Error Handling**: Always handle API errors gracefully
4. **Rate Limiting**: Respect rate limits to avoid being blocked
5. **Caching**: Cache results when appropriate to reduce API calls
6. **Security**: Validate files on the client side before uploading
